[{"id": "blender_api_blender_docs_subset_0", "content": "# Blender Python API Documentation Subset\n\n## Basic Object Creation\n\n### bpy.ops.mesh.primitive_cube_add\nCreates a cube mesh primitive in the current scene.\n\nParameters:\n- size: float, default 2.0 - Size of the cube\n- location: Vector, default (0, 0, 0) - Location for the new cube\n- rotation: Vector, default (0, 0, 0) - Rotation for the new cube\n\nExample:\n```python\nimport bpy\nbpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))\n```\n\n### bpy.ops.mesh.primitive_uv_sphere_add\nCreates a UV sphere mesh primitive in the current scene.\n\nParameters:\n- radius: float, default 1.0 - Radius of the sphere\n- location: Vector, default (0, 0, 0) - Location for the new sphere\n- rotation: Vector, default (0, 0, 0) - Rotation for the new sphere\n- subdivisions: int, default 32 - Number of subdivisions\n\nExample:\n```python\nimport bpy\nbpy.ops.mesh.primitive_uv_sphere_add(radius=1.0, location=(2, 0, 0))\n```\n\n### bpy.ops.mesh.primitive_cylinder_add\nCreates a cylinder mesh primitive in the current scene.", "source": "blender_api", "topic": "Blender Python API Documentation Subset", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_1", "content": "Parameters:\n- radius: float, default 1.0 - Radius of the cylinder\n- depth: float, default 2.0 - Depth (height) of the cylinder\n- location: Vector, default (0, 0, 0) - Location for the new cylinder\n- rotation: Vector, default (0, 0, 0) - Rotation for the new cylinder\n\nExample:\n```python\nimport bpy\nbpy.ops.mesh.primitive_cylinder_add(radius=1.0, depth=2.0, location=(0, 2, 0))\n```\n\n## Object Manipulation\n\n### bpy.context.object\nReference to the currently active object in the scene.\n\nProperties:\n- location: Vector - Object's location in 3D space\n- rotation_euler: Vector - Object's rotation in Euler angles\n- scale: Vector - Object's scale factors\n- name: str - <PERSON>bject's name\n\nExample:\n```python\nimport bpy\nobj = bpy.context.object\nobj.location = (1, 2, 3)\nobj.rotation_euler = (0, 0, 1.57)  # 90 degrees in radians\nobj.scale = (2, 2, 2)\n```\n\n### bpy.data.objects\nCollection of all objects in the current Blender file.", "source": "blender_api", "topic": "Parameters:", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_2", "content": "Methods:\n- new(name, object_data): Create a new object\n- remove(object): Remove an object\n\nExample:\n```python\nimport bpy\n# Get object by name\ncube = bpy.data.objects.get(\"Cube\")\nif cube:\n    bpy.data.objects.remove(cube, do_unlink=True)\n```\n\n## Material System\n\n### bpy.data.materials.new\nCreates a new material.\n\nParameters:\n- name: str - Name of the material\n\nExample:\n```python\nimport bpy\nmaterial = bpy.data.materials.new(name=\"MyMaterial\")\nmaterial.diffuse_color = (1.0, 0.0, 0.0, 1.0)  # Red color\n```\n\n### Object Material Assignment\nAssign materials to objects.\n\nExample:\n```python\nimport bpy\nobj = bpy.context.object\nmaterial = bpy.data.materials.new(name=\"BlueMaterial\")\nmaterial.diffuse_color = (0.0, 0.0, 1.0, 1.0)  # Blue color\n\n# Assign material to object\nif obj.data.materials:\n    obj.data.materials[0] = material\nelse:\n    obj.data.materials.append(material)\n```\n\n## Scene Management\n\n### bpy.context.scene\nReference to the current scene.", "source": "blender_api", "topic": "Methods:", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_3", "content": "Properties:\n- objects: Collection of objects in the scene\n- collection: Main collection of the scene\n\nExample:\n```python\nimport bpy\nscene = bpy.context.scene\nprint(f\"Scene has {len(scene.objects)} objects\")\n```\n\n### bpy.ops.object.select_all\nSelect or deselect all objects in the scene.\n\nParameters:\n- action: str - 'SELECT', 'DESELECT', or 'INVERT'\n\nExample:\n```python\nimport bpy\nbpy.ops.object.select_all(action='DESELECT')\n```\n\n## File Operations\n\n### bpy.ops.wm.save_as_mainfile\nSave the current Blender file.\n\nParameters:\n- filepath: str - Path where to save the file\n\nExample:\n```python\nimport bpy\nbpy.ops.wm.save_as_mainfile(filepath=\"/path/to/file.blend\")\n```\n\n### bpy.ops.export_scene.obj\nExport scene as OBJ file.\n\nParameters:\n- filepath: str - Path where to save the OBJ file\n- use_selection: bool - Export only selected objects\n\nExample:\n```python\nimport bpy\nbpy.ops.export_scene.obj(filepath=\"/path/to/model.obj\", use_selection=False)\n```\n\n## Molecular Nodes (MCP) Basics", "source": "blender_api", "topic": "Properties:", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_4", "content": "### Molecular Nodes Overview\nMolecular Nodes is a Blender add-on for creating and manipulating molecular structures.\n\nKey Concepts:\n- Atoms: Individual atomic elements\n- Bonds: Connections between atoms\n- Molecules: Collections of atoms and bonds\n- Protein structures: Complex molecular assemblies\n\n### Basic Atom Creation\nCreate individual atoms using Molecular Nodes.\n\nExample:\n```python\nimport bpy\n# Note: This requires Molecular Nodes add-on to be installed and enabled\n# Basic atom creation workflow\nbpy.ops.mn.add_atom(element='C', location=(0, 0, 0))  # Carbon atom\nbpy.ops.mn.add_atom(element='O', location=(1.5, 0, 0))  # Oxygen atom\n```\n\n### Molecular Structure Import\nImport molecular structures from common file formats.\n\nSupported formats:\n- PDB (Protein Data Bank)\n- SDF (Structure Data File)\n- MOL (Molecule file)\n\nExample:\n```python\nimport bpy\n# Import PDB file\nbpy.ops.mn.import_pdb(filepath=\"/path/to/structure.pdb\")\n```", "source": "blender_api", "topic": "Molecular Nodes Overview", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_5", "content": "### Bond Creation\nCreate bonds between atoms in molecular structures.\n\nExample:\n```python\nimport bpy\n# Create bond between two selected atoms\nbpy.ops.mn.create_bond(bond_type='SINGLE')\n```\n\n## Common Patterns and Best Practices\n\n### Clear Default Scene\nRemove default objects to start with clean scene.\n\n```python\nimport bpy\n\n# Delete default cube, light, and camera\nbpy.ops.object.select_all(action='SELECT')\nbpy.ops.object.delete(use_global=False)\n\n# Or selectively delete default cube\nif \"Cube\" in bpy.data.objects:\n    bpy.data.objects.remove(bpy.data.objects[\"Cube\"], do_unlink=True)\n```\n\n### Error Handling\nProper error handling in Blender scripts.\n\n```python\nimport bpy\n\ntry:\n    bpy.ops.mesh.primitive_cube_add()\n    print(\"Cube created successfully\")\nexcept Exception as e:\n    print(f\"Error creating cube: {e}\")\n```\n\n### Object Naming and Organization\nBest practices for naming and organizing objects.\n\n```python\nimport bpy", "source": "blender_api", "topic": "Bond Creation", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}, {"id": "blender_api_blender_docs_subset_6", "content": "# Create and name objects systematically\nbpy.ops.mesh.primitive_cube_add()\ncube = bpy.context.object\ncube.name = \"MainCube\"\n\n# Group related objects in collections\ncollection = bpy.data.collections.new(\"Molecules\")\nbpy.context.scene.collection.children.link(collection)\ncollection.objects.link(cube)\n```\n\n### Performance Considerations\nTips for efficient Blender scripting.\n\n- Use bpy.context.evaluated_depsgraph_get() for mesh data access\n- Batch operations when possible\n- Avoid unnecessary scene updates during batch processing\n- Use bmesh for complex mesh operations\n\n```python\nimport bpy\nimport bmesh\n\n# Efficient mesh creation using bmesh\nbm = bmesh.new()\nbmesh.ops.create_cube(bm, size=2.0)\n\nmesh = bpy.data.meshes.new(\"EfficientCube\")\nbm.to_mesh(mesh)\nbm.free()\n\nobj = bpy.data.objects.new(\"EfficientCube\", mesh)\nbpy.context.collection.objects.link(obj)\n```", "source": "blender_api", "topic": "Create and name objects systematically", "subtopic": null, "metadata": {"file": "knowledge_base/blender_docs_subset.txt"}}]