"""
Demo script for BlenderExecutor functionality.

This script demonstrates the capabilities of the BlenderExecutor class
including successful execution, error handling, and output parsing.

Author: AI Agent System
Date: 2025-07-17
"""

import os
import sys
import tempfile
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the path
sys.path.insert(0, os.path.dirname(__file__))

from blender_interface.blender_executor import BlenderExecutor, BlenderExecutionStatus


def demo_successful_execution():
    """Demonstrate successful Blender script execution."""
    print("=" * 60)
    print("Demo 1: Successful Blender Script Execution")
    print("=" * 60)
    
    # Create a simple Blender script
    script_content = """
import bpy
import os

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))
cube = bpy.context.active_object
cube.name = "DemoCube"

# Create a sphere
bpy.ops.mesh.primitive_uv_sphere_add(radius=1.0, location=(3, 0, 0))
sphere = bpy.context.active_object
sphere.name = "DemoSphere"

# Create a cylinder
bpy.ops.mesh.primitive_cylinder_add(radius=0.8, depth=2.0, location=(-3, 0, 0))
cylinder = bpy.context.active_object
cylinder.name = "DemoCylinder"

# Save the file
output_path = "/tmp/demo_objects.blend"
bpy.ops.wm.save_as_mainfile(filepath=output_path)

print(f"Created 3 objects")
print(f"Successfully saved Blender file to {output_path}")
print("Demo execution completed successfully!")
"""
    
    try:
        # Initialize BlenderExecutor
        executor = BlenderExecutor()
        print(f"Using Blender: {executor.blender_path}")
        print(f"Blender Version: {executor.get_blender_version()}")
        
        # Execute the script
        print("\nExecuting Blender script...")
        output = executor.execute_script(script_content, output_dir="/tmp")
        
        # Display results
        print(f"\nExecution Status: {output.status.value}")
        print(f"Return Code: {output.return_code}")
        print(f"Execution Time: {output.execution_time:.2f} seconds")
        print(f"Output Files: {len(output.output_files)}")
        
        if output.output_files:
            print("Generated Files:")
            for file_path in output.output_files:
                print(f"  - {file_path}")
        
        if output.parsed_info:
            print("Parsed Information:")
            for key, value in output.parsed_info.items():
                print(f"  - {key}: {value}")
        
        print("\nBlender Output:")
        print(output.stdout)
        
        if output.stderr:
            print("\nBlender Errors:")
            print(output.stderr)
        
        return output.status == BlenderExecutionStatus.SUCCESS
        
    except Exception as e:
        print(f"Error: {e}")
        return False


def demo_error_handling():
    """Demonstrate error handling with problematic script."""
    print("\n" + "=" * 60)
    print("Demo 2: Error Handling")
    print("=" * 60)
    
    # Create a script with errors
    error_script = """
import bpy

# This will cause a NameError
undefined_variable.some_method()

# This will cause a TypeError if reached
bpy.ops.mesh.primitive_cube_add(invalid_parameter="invalid")

# This will cause an AttributeError if reached
None.location = (0, 0, 0)
"""
    
    try:
        executor = BlenderExecutor()
        
        print("Executing script with errors...")
        output = executor.execute_script(error_script)
        
        # Display results
        print(f"\nExecution Status: {output.status.value}")
        print(f"Return Code: {output.return_code}")
        print(f"Execution Time: {output.execution_time:.2f} seconds")
        
        if output.error_details:
            print("\nError Details:")
            for error_type, details in output.error_details.items():
                print(f"  - {error_type}: {details}")
        
        print("\nBlender Stderr:")
        print(output.stderr)
        
        return output.status in [BlenderExecutionStatus.ERROR, BlenderExecutionStatus.SCRIPT_ERROR]
        
    except Exception as e:
        print(f"Error: {e}")
        return False


def demo_script_file_execution():
    """Demonstrate executing script from file."""
    print("\n" + "=" * 60)
    print("Demo 3: Script File Execution")
    print("=" * 60)
    
    # Create a temporary script file
    script_content = """
import bpy

# Clear scene
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a simple scene
bpy.ops.mesh.primitive_monkey_add(location=(0, 0, 0))
monkey = bpy.context.active_object
monkey.name = "Suzanne"

# Add a light
bpy.ops.object.light_add(type='SUN', location=(4, 4, 4))

# Add a camera
bpy.ops.object.camera_add(location=(7, -7, 5))
camera = bpy.context.active_object
camera.rotation_euler = (1.1, 0, 0.785)

# Save the scene
bpy.ops.wm.save_as_mainfile(filepath="/tmp/monkey_scene.blend")
print("Created monkey scene with lighting and camera")
print("Successfully saved to /tmp/monkey_scene.blend")
"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
        temp_file.write(script_content)
        temp_file_path = temp_file.name
    
    try:
        executor = BlenderExecutor()
        
        print(f"Executing script from file: {temp_file_path}")
        output = executor.execute_script_file(temp_file_path, output_dir="/tmp")
        
        # Display results
        print(f"\nExecution Status: {output.status.value}")
        print(f"Return Code: {output.return_code}")
        print(f"Execution Time: {output.execution_time:.2f} seconds")
        print(f"Output Files: {len(output.output_files)}")
        
        if output.output_files:
            print("Generated Files:")
            for file_path in output.output_files:
                print(f"  - {file_path}")
        
        print("\nBlender Output:")
        print(output.stdout)
        
        return output.status == BlenderExecutionStatus.SUCCESS
        
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_file_path)
        except OSError:
            pass


def demo_output_parsing():
    """Demonstrate output parsing capabilities."""
    print("\n" + "=" * 60)
    print("Demo 4: Output Parsing")
    print("=" * 60)
    
    # Create a script that generates various types of output
    parsing_script = """
import bpy
import time

# Clear scene
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create multiple objects
for i in range(5):
    bpy.ops.mesh.primitive_cube_add(location=(i*2, 0, 0))

print("Created 5 objects")

# Create materials
for i in range(3):
    mat = bpy.data.materials.new(name=f"Material_{i}")
    
print("Created 3 materials")

# Create meshes
for i in range(3):
    mesh = bpy.data.meshes.new(f"Mesh_{i}")
    
print("Created 3 meshes")

# Save multiple files
bpy.ops.wm.save_as_mainfile(filepath="/tmp/parsing_demo.blend")
print("Saved '/tmp/parsing_demo.blend'")

# Simulate some processing time
time.sleep(0.1)

print("Processing completed")
print("Time: 00:05.23")
print("Sample 64/128")
print("Mem:512.3M")
"""
    
    try:
        executor = BlenderExecutor()
        
        print("Executing script with rich output...")
        output = executor.execute_script(parsing_script, output_dir="/tmp")
        
        # Display results
        print(f"\nExecution Status: {output.status.value}")
        print(f"Return Code: {output.return_code}")
        print(f"Execution Time: {output.execution_time:.2f} seconds")
        
        if output.parsed_info:
            print("\nParsed Information:")
            for key, value in output.parsed_info.items():
                print(f"  - {key}: {value}")
        
        print(f"\nOutput Files Found: {len(output.output_files)}")
        for file_path in output.output_files:
            print(f"  - {file_path}")
        
        print("\nFull Blender Output:")
        print(output.stdout)
        
        return output.status == BlenderExecutionStatus.SUCCESS
        
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Run all demos."""
    print("BlenderExecutor Functionality Demo")
    print("=" * 60)
    
    demos = [
        ("Successful Execution", demo_successful_execution),
        ("Error Handling", demo_error_handling),
        ("Script File Execution", demo_script_file_execution),
        ("Output Parsing", demo_output_parsing)
    ]
    
    results = []
    
    for demo_name, demo_func in demos:
        try:
            success = demo_func()
            results.append((demo_name, success))
        except Exception as e:
            print(f"Demo '{demo_name}' failed with exception: {e}")
            results.append((demo_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("Demo Results Summary")
    print("=" * 60)
    
    for demo_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{demo_name}: {status}")
    
    total_passed = sum(1 for _, success in results if success)
    total_demos = len(results)
    
    print(f"\nOverall: {total_passed}/{total_demos} demos passed")
    
    if total_passed == total_demos:
        print("🎉 All demos completed successfully!")
        return True
    else:
        print("⚠️  Some demos failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
