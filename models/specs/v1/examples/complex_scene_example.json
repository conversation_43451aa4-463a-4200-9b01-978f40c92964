{"schema_version": "v1.0.0", "model_info": {"name": "Complex Multi-Object Scene", "description": "A scene with multiple objects demonstrating hierarchical relationships and various materials", "created_at": "2025-01-17T11:00:00Z", "tags": ["complex", "multi-object", "hierarchy", "scene"]}, "scene_settings": {"units": "meters", "background_color": {"r": 0.1, "g": 0.15, "b": 0.2, "a": 1.0}}, "objects": [{"id": "base_plane", "name": "Ground Plane", "geometry": {"type": "plane", "size": 10.0}, "transform": {"position": {"x": 0.0, "y": 0.0, "z": -0.1}}, "material": {"type": "pbr", "name": "Ground Material", "color": {"r": 0.3, "g": 0.3, "b": 0.3, "a": 1.0}, "metallic": 0.0, "roughness": 0.9}, "visible": true}, {"id": "central_cube", "name": "Central Cube", "geometry": {"type": "cube", "size": 1.5}, "transform": {"position": {"x": 0.0, "y": 0.0, "z": 0.75}}, "material": {"type": "pbr", "name": "Central Material", "color": {"r": 0.7, "g": 0.2, "b": 0.2, "a": 1.0}, "metallic": 0.3, "roughness": 0.4}, "visible": true}, {"id": "child_sphere", "name": "Child Sphere", "geometry": {"type": "sphere", "radius": 0.3, "subdivisions": 4}, "transform": {"position": {"x": 1.0, "y": 1.0, "z": 0.3}}, "material": {"type": "basic", "name": "Child Material", "color": {"r": 0.2, "g": 0.7, "b": 0.2, "a": 1.0}}, "visible": true, "parent_id": "central_cube"}, {"id": "orbiting_cylinder", "name": "Orbiting Cylinder", "geometry": {"type": "cylinder", "radius": 0.2, "height": 1.0, "vertices": 12}, "transform": {"position": {"x": 2.5, "y": 0.0, "z": 0.5}, "rotation": {"x": 1.5708, "y": 0.0, "z": 0.0}}, "material": {"type": "pbr", "name": "Metallic Blue", "color": {"r": 0.1, "g": 0.4, "b": 0.8, "a": 1.0}, "metallic": 0.8, "roughness": 0.2}, "visible": true}]}