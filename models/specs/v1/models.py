"""
Pydantic models for 3D model specification validation.

This module provides Pydantic models that correspond to the JSON Schema
defined in base_model_spec.json, enabling runtime validation and type checking.
"""

from datetime import datetime
from enum import Enum
from typing import List, Optional, Union
from pydantic import BaseModel, Field, validator, root_validator
import re


class UnitsEnum(str, Enum):
    """Supported units for scene measurements."""
    METERS = "meters"
    CENTIMETERS = "centimeters"
    MILLIMETERS = "millimeters"
    INCHES = "inches"
    FEET = "feet"


class GeometryTypeEnum(str, Enum):
    """Supported basic geometry types."""
    CUBE = "cube"
    SPHERE = "sphere"
    CYLINDER = "cylinder"
    PLANE = "plane"
    CONE = "cone"


class MaterialTypeEnum(str, Enum):
    """Supported material types."""
    BASIC = "basic"
    PBR = "pbr"


class Vector3(BaseModel):
    """3D vector representation."""
    x: float = Field(..., description="X coordinate")
    y: float = Field(..., description="Y coordinate")
    z: float = Field(..., description="Z coordinate")

    class Config:
        extra = "forbid"


class Color(BaseModel):
    """RGBA color representation."""
    r: float = Field(..., ge=0.0, le=1.0, description="Red component (0.0 to 1.0)")
    g: float = Field(..., ge=0.0, le=1.0, description="Green component (0.0 to 1.0)")
    b: float = Field(..., ge=0.0, le=1.0, description="Blue component (0.0 to 1.0)")
    a: float = Field(1.0, ge=0.0, le=1.0, description="Alpha component (0.0 to 1.0)")

    class Config:
        extra = "forbid"


class Transform(BaseModel):
    """Transformation properties for 3D objects."""
    position: Optional[Vector3] = Field(None, description="Position in 3D space")
    rotation: Optional[Vector3] = Field(None, description="Rotation in Euler angles (radians)")
    scale: Optional[Vector3] = Field(None, description="Scale factors for each axis")

    class Config:
        extra = "forbid"


class Material(BaseModel):
    """Material properties for 3D objects."""
    type: MaterialTypeEnum = Field(..., description="Type of material")
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Name of the material")
    color: Optional[Color] = Field(None, description="Base color of the material")
    metallic: float = Field(0.0, ge=0.0, le=1.0, description="Metallic factor (0.0 = dielectric, 1.0 = metallic)")
    roughness: float = Field(0.5, ge=0.0, le=1.0, description="Roughness factor (0.0 = mirror, 1.0 = completely rough)")
    emission: Optional[Color] = Field(None, description="Emission color for glowing materials")

    class Config:
        extra = "forbid"


class BaseGeometry(BaseModel):
    """Base class for all geometry types."""
    type: GeometryTypeEnum = Field(..., description="Type of basic geometry")

    class Config:
        extra = "forbid"


class CubeGeometry(BaseGeometry):
    """Cube geometry specification."""
    type: GeometryTypeEnum = Field(GeometryTypeEnum.CUBE, const=True)
    size: float = Field(2.0, gt=0.0, description="Size of the cube (edge length)")


class SphereGeometry(BaseGeometry):
    """Sphere geometry specification."""
    type: GeometryTypeEnum = Field(GeometryTypeEnum.SPHERE, const=True)
    radius: float = Field(1.0, gt=0.0, description="Radius of the sphere")
    subdivisions: int = Field(4, ge=3, le=10, description="Number of subdivisions for sphere mesh")


class CylinderGeometry(BaseGeometry):
    """Cylinder geometry specification."""
    type: GeometryTypeEnum = Field(GeometryTypeEnum.CYLINDER, const=True)
    radius: float = Field(1.0, gt=0.0, description="Radius of the cylinder")
    height: float = Field(2.0, gt=0.0, description="Height of the cylinder")
    vertices: int = Field(32, ge=3, le=64, description="Number of vertices around the cylinder")


class PlaneGeometry(BaseGeometry):
    """Plane geometry specification."""
    type: GeometryTypeEnum = Field(GeometryTypeEnum.PLANE, const=True)
    size: float = Field(2.0, gt=0.0, description="Size of the plane (edge length)")


class ConeGeometry(BaseGeometry):
    """Cone geometry specification."""
    type: GeometryTypeEnum = Field(GeometryTypeEnum.CONE, const=True)
    radius: float = Field(1.0, gt=0.0, description="Radius of the cone base")
    height: float = Field(2.0, gt=0.0, description="Height of the cone")
    vertices: int = Field(32, ge=3, le=64, description="Number of vertices around the cone base")


# Union type for all geometry types
Geometry = Union[CubeGeometry, SphereGeometry, CylinderGeometry, PlaneGeometry, ConeGeometry]


class Object3D(BaseModel):
    """3D object specification."""
    id: str = Field(..., min_length=1, max_length=50, description="Unique identifier for the object")
    name: str = Field(..., min_length=1, max_length=100, description="Human-readable name for the object")
    geometry: Geometry = Field(..., description="Geometric properties of the object")
    transform: Optional[Transform] = Field(None, description="Transformation properties")
    material: Optional[Material] = Field(None, description="Material properties of the object")
    visible: bool = Field(True, description="Whether the object is visible in the scene")
    parent_id: Optional[str] = Field(None, description="ID of the parent object for hierarchical relationships")

    @validator('id')
    def validate_id(cls, v):
        """Validate object ID format."""
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('ID must contain only alphanumeric characters, underscores, and hyphens')
        return v

    @validator('parent_id')
    def validate_parent_id(cls, v):
        """Validate parent ID format."""
        if v is not None and not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Parent ID must contain only alphanumeric characters, underscores, and hyphens')
        return v

    class Config:
        extra = "forbid"


class ModelInfo(BaseModel):
    """Model metadata information."""
    name: str = Field(..., min_length=1, max_length=100, description="Name of the 3D model")
    description: str = Field(..., max_length=500, description="Description of the 3D model")
    created_at: Optional[datetime] = Field(None, description="When the model spec was created")
    tags: Optional[List[str]] = Field(None, max_items=10, description="Tags for categorizing the model")

    @validator('tags')
    def validate_tags(cls, v):
        """Validate tags format."""
        if v is not None:
            for tag in v:
                if not tag or len(tag) > 50:
                    raise ValueError('Each tag must be between 1 and 50 characters')
        return v

    class Config:
        extra = "forbid"


class SceneSettings(BaseModel):
    """Scene-level settings."""
    units: UnitsEnum = Field(UnitsEnum.METERS, description="Units used for measurements in the scene")
    background_color: Optional[Color] = Field(None, description="Background color of the scene")

    class Config:
        extra = "forbid"


class ModelSpecification(BaseModel):
    """Complete 3D model specification."""
    schema_version: str = Field(..., description="Version of the schema being used")
    model_info: ModelInfo = Field(..., description="Model metadata")
    scene_settings: Optional[SceneSettings] = Field(None, description="Scene-level settings")
    objects: List[Object3D] = Field(..., min_items=1, description="Array of 3D objects in the model")

    @validator('schema_version')
    def validate_schema_version(cls, v):
        """Validate schema version format."""
        if not re.match(r'^v1\.[0-9]+\.[0-9]+$', v):
            raise ValueError('Schema version must follow format v1.x.y')
        return v

    @root_validator
    def validate_object_hierarchy(cls, values):
        """Validate object parent-child relationships."""
        objects = values.get('objects', [])
        if not objects:
            return values

        # Create a set of all object IDs
        object_ids = {obj.id for obj in objects}

        # Check that all parent_ids reference existing objects
        for obj in objects:
            if obj.parent_id and obj.parent_id not in object_ids:
                raise ValueError(f'Object {obj.id} references non-existent parent {obj.parent_id}')

        # Check for circular references (simplified check)
        for obj in objects:
            if obj.parent_id == obj.id:
                raise ValueError(f'Object {obj.id} cannot be its own parent')

        return values

    class Config:
        extra = "forbid"
