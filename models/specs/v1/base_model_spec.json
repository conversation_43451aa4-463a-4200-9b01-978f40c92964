{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://blender-ai-agent.com/schemas/v1/base_model_spec.json", "title": "Base 3D Model Specification", "description": "JSON Schema for basic 3D model specifications supporting fundamental geometries with basic properties", "version": "1.0.0", "type": "object", "required": ["schema_version", "model_info", "objects"], "properties": {"schema_version": {"type": "string", "pattern": "^v1\\.[0-9]+\\.[0-9]+$", "description": "Version of the schema being used", "examples": ["v1.0.0", "v1.1.0"]}, "model_info": {"type": "object", "required": ["name", "description"], "properties": {"name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Name of the 3D model"}, "description": {"type": "string", "maxLength": 500, "description": "Description of the 3D model"}, "created_at": {"type": "string", "format": "date-time", "description": "ISO 8601 timestamp when the model spec was created"}, "tags": {"type": "array", "items": {"type": "string", "minLength": 1, "maxLength": 50}, "maxItems": 10, "description": "Tags for categorizing the model"}}}, "scene_settings": {"type": "object", "properties": {"units": {"type": "string", "enum": ["meters", "centimeters", "millimeters", "inches", "feet"], "default": "meters", "description": "Units used for measurements in the scene"}, "background_color": {"$ref": "#/definitions/color", "description": "Background color of the scene"}}}, "objects": {"type": "array", "minItems": 1, "items": {"$ref": "#/definitions/object"}, "description": "Array of 3D objects in the model"}}, "definitions": {"vector3": {"type": "object", "required": ["x", "y", "z"], "properties": {"x": {"type": "number", "description": "X coordinate"}, "y": {"type": "number", "description": "Y coordinate"}, "z": {"type": "number", "description": "Z coordinate"}}, "additionalProperties": false}, "color": {"type": "object", "required": ["r", "g", "b"], "properties": {"r": {"type": "number", "minimum": 0, "maximum": 1, "description": "Red component (0.0 to 1.0)"}, "g": {"type": "number", "minimum": 0, "maximum": 1, "description": "Green component (0.0 to 1.0)"}, "b": {"type": "number", "minimum": 0, "maximum": 1, "description": "Blue component (0.0 to 1.0)"}, "a": {"type": "number", "minimum": 0, "maximum": 1, "default": 1.0, "description": "Alpha component (0.0 to 1.0)"}}, "additionalProperties": false}, "transform": {"type": "object", "properties": {"position": {"$ref": "#/definitions/vector3", "description": "Position in 3D space"}, "rotation": {"$ref": "#/definitions/vector3", "description": "Rotation in Euler angles (radians)"}, "scale": {"$ref": "#/definitions/vector3", "description": "Scale factors for each axis"}}, "additionalProperties": false}, "material": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["basic", "pbr"], "description": "Type of material"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Name of the material"}, "color": {"$ref": "#/definitions/color", "description": "Base color of the material"}, "metallic": {"type": "number", "minimum": 0, "maximum": 1, "default": 0, "description": "Metallic factor (0.0 = dielectric, 1.0 = metallic)"}, "roughness": {"type": "number", "minimum": 0, "maximum": 1, "default": 0.5, "description": "Roughness factor (0.0 = mirror, 1.0 = completely rough)"}, "emission": {"$ref": "#/definitions/color", "description": "Emission color for glowing materials"}}, "additionalProperties": false}, "geometry": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["cube", "sphere", "cylinder", "plane", "cone"], "description": "Type of basic geometry"}}, "allOf": [{"if": {"properties": {"type": {"const": "cube"}}}, "then": {"properties": {"size": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 2.0, "description": "Size of the cube (edge length)"}}}}, {"if": {"properties": {"type": {"const": "sphere"}}}, "then": {"properties": {"radius": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 1.0, "description": "<PERSON>dius of the sphere"}, "subdivisions": {"type": "integer", "minimum": 3, "maximum": 10, "default": 4, "description": "Number of subdivisions for sphere mesh"}}}}, {"if": {"properties": {"type": {"const": "cylinder"}}}, "then": {"properties": {"radius": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 1.0, "description": "Radius of the cylinder"}, "height": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 2.0, "description": "Height of the cylinder"}, "vertices": {"type": "integer", "minimum": 3, "maximum": 64, "default": 32, "description": "Number of vertices around the cylinder"}}}}, {"if": {"properties": {"type": {"const": "plane"}}}, "then": {"properties": {"size": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 2.0, "description": "Size of the plane (edge length)"}}}}, {"if": {"properties": {"type": {"const": "cone"}}}, "then": {"properties": {"radius": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 1.0, "description": "Radius of the cone base"}, "height": {"type": "number", "minimum": 0, "exclusiveMinimum": true, "default": 2.0, "description": "Height of the cone"}, "vertices": {"type": "integer", "minimum": 3, "maximum": 64, "default": 32, "description": "Number of vertices around the cone base"}}}}]}, "object": {"type": "object", "required": ["id", "name", "geometry"], "properties": {"id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "minLength": 1, "maxLength": 50, "description": "Unique identifier for the object"}, "name": {"type": "string", "minLength": 1, "maxLength": 100, "description": "Human-readable name for the object"}, "geometry": {"$ref": "#/definitions/geometry", "description": "Geometric properties of the object"}, "transform": {"$ref": "#/definitions/transform", "description": "Transformation properties (position, rotation, scale)"}, "material": {"$ref": "#/definitions/material", "description": "Material properties of the object"}, "visible": {"type": "boolean", "default": true, "description": "Whether the object is visible in the scene"}, "parent_id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$", "description": "ID of the parent object for hierarchical relationships"}}, "additionalProperties": false}}}