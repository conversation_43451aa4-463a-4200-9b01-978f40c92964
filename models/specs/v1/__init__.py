"""
Version 1 of the 3D model specification schema.

This module provides the JSON Schema definition and Pydantic models
for validating 3D model specifications.
"""

from .models import (
    ModelSpecification,
    Object3D,
    Geometry,
    CubeGeometry,
    SphereGeometry,
    CylinderGeometry,
    PlaneGeometry,
    ConeGeometry,
    Material,
    Transform,
    Vector3,
    Color,
    ModelInfo,
    SceneSettings,
    UnitsEnum,
    GeometryTypeEnum,
    MaterialTypeEnum
)

__all__ = [
    'ModelSpecification',
    'Object3D',
    'Geometry',
    'CubeGeometry',
    'SphereGeometry',
    'CylinderGeometry',
    'PlaneGeometry',
    'ConeGeometry',
    'Material',
    'Transform',
    'Vector3',
    'Color',
    'ModelInfo',
    'SceneSettings',
    'UnitsEnum',
    'GeometryTypeEnum',
    'MaterialTypeEnum'
]
