"""
Schema version management utilities.

This module provides tools for managing schema versions, validation,
and migration between different schema versions.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import re


@dataclass
class SchemaVersion:
    """Represents a schema version with metadata."""
    version: str
    major: int
    minor: int
    patch: int
    path: Path
    description: str
    created_at: datetime
    breaking_changes: bool = False


class SchemaVersionManager:
    """Manages schema versions and provides validation utilities."""
    
    def __init__(self, specs_root: Optional[Path] = None):
        """Initialize the version manager."""
        if specs_root is None:
            specs_root = Path(__file__).parent
        self.specs_root = Path(specs_root)
        self._versions_cache: Optional[List[SchemaVersion]] = None
    
    def get_available_versions(self) -> List[SchemaVersion]:
        """Get all available schema versions."""
        if self._versions_cache is not None:
            return self._versions_cache
        
        versions = []
        
        # Scan for version directories (v1, v2, etc.)
        for version_dir in self.specs_root.iterdir():
            if not version_dir.is_dir():
                continue
            
            version_match = re.match(r'^v(\d+)$', version_dir.name)
            if not version_match:
                continue
            
            major_version = int(version_match.group(1))
            
            # Look for schema files in the version directory
            schema_file = version_dir / "base_model_spec.json"
            if not schema_file.exists():
                continue
            
            # Try to extract version info from schema file
            try:
                with open(schema_file, 'r') as f:
                    schema_data = json.load(f)
                
                schema_version = schema_data.get('version', f'{major_version}.0.0')
                version_parts = schema_version.split('.')
                
                if len(version_parts) >= 3:
                    minor = int(version_parts[1])
                    patch = int(version_parts[2])
                else:
                    minor = 0
                    patch = 0
                
                # Get metadata
                description = schema_data.get('description', 'No description available')
                
                # Get file creation time as fallback
                created_at = datetime.fromtimestamp(schema_file.stat().st_mtime)
                
                version_obj = SchemaVersion(
                    version=f"v{major_version}.{minor}.{patch}",
                    major=major_version,
                    minor=minor,
                    patch=patch,
                    path=schema_file,
                    description=description,
                    created_at=created_at
                )
                
                versions.append(version_obj)
                
            except (json.JSONDecodeError, ValueError, KeyError) as e:
                print(f"Warning: Could not parse schema version from {schema_file}: {e}")
                continue
        
        # Sort versions by major.minor.patch
        versions.sort(key=lambda v: (v.major, v.minor, v.patch))
        self._versions_cache = versions
        return versions
    
    def get_latest_version(self) -> Optional[SchemaVersion]:
        """Get the latest available schema version."""
        versions = self.get_available_versions()
        return versions[-1] if versions else None
    
    def get_version(self, version_string: str) -> Optional[SchemaVersion]:
        """Get a specific schema version."""
        if not version_string.startswith('v'):
            version_string = f'v{version_string}'
        
        versions = self.get_available_versions()
        for version in versions:
            if version.version == version_string:
                return version
        return None
    
    def validate_version_format(self, version_string: str) -> bool:
        """Validate that a version string follows the expected format."""
        pattern = r'^v?(\d+)\.(\d+)\.(\d+)$'
        return bool(re.match(pattern, version_string))
    
    def is_compatible(self, from_version: str, to_version: str) -> bool:
        """Check if two schema versions are compatible."""
        from_ver = self.get_version(from_version)
        to_ver = self.get_version(to_version)
        
        if not from_ver or not to_ver:
            return False
        
        # Same major version is generally compatible
        if from_ver.major != to_ver.major:
            return False
        
        return from_ver.minor <= to_ver.minor
    
    def load_schema(self, version: Optional[str] = None) -> Dict[str, Any]:
        """Load a schema by version."""
        if version is None:
            schema_version = self.get_latest_version()
        else:
            schema_version = self.get_version(version)
        
        if not schema_version:
            raise ValueError(f"Schema version {version} not found")
        
        with open(schema_version.path, 'r') as f:
            return json.load(f)
    
    def validate_specification(self, spec_data: Dict[str, Any], 
                             version: Optional[str] = None) -> Tuple[bool, List[str]]:
        """Validate a specification against a schema version."""
        try:
            # Import here to avoid circular imports
            from .v1.models import ModelSpecification
            
            # For now, we only support v1.x.x validation
            if version and not version.startswith('v1'):
                return False, [f"Validation for version {version} not yet implemented"]
            
            # Validate using Pydantic model
            ModelSpecification(**spec_data)
            return True, []
            
        except Exception as e:
            return False, [str(e)]


# Global instance for easy access
version_manager = SchemaVersionManager()


def validate_spec(spec_data: Dict[str, Any], version: Optional[str] = None) -> Tuple[bool, List[str]]:
    """Convenience function to validate a specification."""
    return version_manager.validate_specification(spec_data, version)
