# 3D Model Specification Schema

This directory contains the JSON Schema definitions and validation tools for 3D model specifications used in the Blender AI Agent system.

## Overview

The schema system provides:
- **JSON Schema definitions** for validating 3D model specifications
- **Pydantic models** for runtime validation and type checking
- **Version management** for schema evolution
- **Example specifications** for testing and reference

## Schema Structure

### Version 1.0.0 (`v1/`)

The current schema supports basic 3D geometries with the following structure:

```json
{
  "schema_version": "v1.0.0",
  "model_info": {
    "name": "Model Name",
    "description": "Model description",
    "created_at": "2025-01-17T10:00:00Z",
    "tags": ["tag1", "tag2"]
  },
  "scene_settings": {
    "units": "meters",
    "background_color": {"r": 0.2, "g": 0.2, "b": 0.2, "a": 1.0}
  },
  "objects": [
    {
      "id": "unique_id",
      "name": "Object Name",
      "geometry": {
        "type": "cube|sphere|cylinder|plane|cone",
        // geometry-specific properties
      },
      "transform": {
        "position": {"x": 0, "y": 0, "z": 0},
        "rotation": {"x": 0, "y": 0, "z": 0},
        "scale": {"x": 1, "y": 1, "z": 1}
      },
      "material": {
        "type": "basic|pbr",
        "color": {"r": 1, "g": 0, "b": 0, "a": 1},
        "metallic": 0.0,
        "roughness": 0.5
      },
      "visible": true,
      "parent_id": "optional_parent_id"
    }
  ]
}
```

### Supported Geometries

1. **Cube**: `{"type": "cube", "size": 2.0}`
2. **Sphere**: `{"type": "sphere", "radius": 1.0, "subdivisions": 4}`
3. **Cylinder**: `{"type": "cylinder", "radius": 1.0, "height": 2.0, "vertices": 32}`
4. **Plane**: `{"type": "plane", "size": 2.0}`
5. **Cone**: `{"type": "cone", "radius": 1.0, "height": 2.0, "vertices": 32}`

### Material Types

1. **Basic**: Simple color-based material
2. **PBR**: Physically-based rendering material with metallic and roughness properties

## Usage

### Python Validation

```python
from models.specs.v1.models import ModelSpecification
from models.specs.version_manager import validate_spec
import json

# Load a specification
with open('example.json', 'r') as f:
    spec_data = json.load(f)

# Validate using Pydantic model
try:
    spec = ModelSpecification(**spec_data)
    print("Specification is valid!")
except ValueError as e:
    print(f"Validation error: {e}")

# Validate using version manager
is_valid, errors = validate_spec(spec_data)
if is_valid:
    print("Specification is valid!")
else:
    print(f"Validation errors: {errors}")
```

### Creating Specifications Programmatically

```python
from models.specs.v1.models import (
    ModelSpecification, ModelInfo, Object3D, 
    CubeGeometry, Material, Color, MaterialTypeEnum
)

# Create a simple cube specification
spec = ModelSpecification(
    schema_version="v1.0.0",
    model_info=ModelInfo(
        name="Red Cube",
        description="A simple red cube"
    ),
    objects=[
        Object3D(
            id="cube_01",
            name="Red Cube",
            geometry=CubeGeometry(size=2.0),
            material=Material(
                type=MaterialTypeEnum.BASIC,
                color=Color(r=1.0, g=0.0, b=0.0)
            )
        )
    ]
)

# Convert to dictionary for JSON serialization
spec_dict = spec.dict()
```

## Version Management

The schema system includes version management capabilities:

```python
from models.specs.version_manager import SchemaVersionManager

vm = SchemaVersionManager()

# Get available versions
versions = vm.get_available_versions()
print(f"Available versions: {[v.version for v in versions]}")

# Get latest version
latest = vm.get_latest_version()
print(f"Latest version: {latest.version}")

# Check compatibility
is_compatible = vm.is_compatible("v1.0.0", "v1.1.0")
print(f"v1.0.0 -> v1.1.0 compatible: {is_compatible}")

# Load schema
schema = vm.load_schema("v1.0.0")
```

## Examples

The `v1/examples/` directory contains example specifications:

- `cube_example.json` - Simple red cube
- `sphere_example.json` - Blue metallic sphere with PBR material
- `cylinder_example.json` - Green cylinder with custom dimensions
- `cone_example.json` - Orange cone with emission properties
- `complex_scene_example.json` - Multi-object scene with hierarchy

## Testing

Run the test suite to validate the schema system:

```bash
# Run all schema tests
python -m pytest tests/test_model_spec_schema.py -v

# Run specific test categories
python -m pytest tests/test_model_spec_schema.py::TestBasicModels -v
python -m pytest tests/test_model_spec_schema.py::TestExampleValidation -v
```

## Schema Evolution

When creating new schema versions:

1. Create a new version directory (e.g., `v2/`)
2. Update the JSON Schema file
3. Create corresponding Pydantic models
4. Add migration utilities if needed
5. Update the version manager
6. Add comprehensive tests

### Version Compatibility Rules

- **Major version changes** (v1 -> v2): Breaking changes, not backward compatible
- **Minor version changes** (v1.0 -> v1.1): New features, backward compatible
- **Patch version changes** (v1.0.0 -> v1.0.1): Bug fixes, fully compatible

## Integration with Blender AI Agent System

This schema system integrates with the broader Blender AI Agent system:

1. **Specification Generation Agent** uses these models to generate valid specifications
2. **Specification-to-Code Agent** reads these specifications to generate Blender Python code
3. **Validation systems** ensure data integrity throughout the pipeline

## Future Enhancements

Planned improvements for future versions:

- Support for complex materials and textures
- Animation and keyframe specifications
- Molecular Nodes (MCP) specific structures
- Advanced lighting and camera settings
- Modifier specifications (subdivision, bevel, etc.)
- Custom mesh data support
