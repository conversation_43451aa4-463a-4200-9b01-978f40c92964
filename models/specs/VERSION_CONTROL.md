# Schema Version Control Guidelines

This document outlines the version control process for 3D model specification schemas in the Blender AI Agent system.

## Version Numbering Scheme

We follow semantic versioning (SemVer) with a prefix format: `vMAJOR.MINOR.PATCH`

### Version Components

- **MAJOR** (`v1`, `v2`, etc.): Breaking changes that are not backward compatible
- **MINOR** (`v1.1`, `v1.2`, etc.): New features that are backward compatible
- **PATCH** (`v1.0.1`, `v1.0.2`, etc.): Bug fixes and minor improvements

### Examples

- `v1.0.0` - Initial release
- `v1.1.0` - Added new geometry types (backward compatible)
- `v1.0.1` - Fixed validation bug (backward compatible)
- `v2.0.0` - Major restructure (breaking changes)

## Directory Structure

```
models/specs/
├── v1/                          # Version 1.x.x
│   ├── base_model_spec.json     # JSON Schema definition
│   ├── models.py                # Pydantic models
│   ├── examples/                # Example specifications
│   └── __init__.py
├── v2/                          # Version 2.x.x (future)
│   └── ...
├── version_manager.py           # Version management utilities
├── README.md                    # Usage documentation
└── VERSION_CONTROL.md          # This file
```

## Version Control Process

### 1. Creating a New Major Version

When introducing breaking changes:

1. **Create new version directory**:
   ```bash
   mkdir models/specs/v2
   ```

2. **Copy and modify schema**:
   ```bash
   cp models/specs/v1/base_model_spec.json models/specs/v2/
   # Edit the new schema file
   ```

3. **Update schema metadata**:
   ```json
   {
     "$schema": "http://json-schema.org/draft-07/schema#",
     "$id": "https://blender-ai-agent.com/schemas/v2/base_model_spec.json",
     "version": "2.0.0",
     ...
   }
   ```

4. **Create new Pydantic models**:
   ```bash
   cp models/specs/v1/models.py models/specs/v2/
   # Update models for new schema
   ```

5. **Update version manager** to support the new version

6. **Create migration utilities** if needed

### 2. Creating a New Minor Version

When adding backward-compatible features:

1. **Update existing schema file** in the current major version directory

2. **Increment version number**:
   ```json
   {
     "version": "1.1.0",
     ...
   }
   ```

3. **Update Pydantic models** to support new features

4. **Add new examples** demonstrating the new features

5. **Update tests** to cover new functionality

### 3. Creating a Patch Version

When fixing bugs or making minor improvements:

1. **Update schema or models** with fixes

2. **Increment patch version**:
   ```json
   {
     "version": "1.0.1",
     ...
   }
   ```

3. **Update tests** if necessary

## Compatibility Guidelines

### Backward Compatibility Rules

1. **MUST NOT** remove required fields
2. **MUST NOT** change field types in incompatible ways
3. **MUST NOT** change validation constraints to be more restrictive
4. **MAY** add new optional fields
5. **MAY** add new enum values
6. **MAY** relax validation constraints

### Breaking Changes (Require Major Version)

- Removing or renaming fields
- Changing field types
- Making optional fields required
- Changing validation rules to be more restrictive
- Restructuring the schema hierarchy

### Non-Breaking Changes (Minor/Patch Version)

- Adding new optional fields
- Adding new geometry types
- Adding new material types
- Relaxing validation constraints
- Bug fixes in validation logic

## Migration Strategy

### Automatic Migration

For minor version updates, the system should automatically handle migration:

```python
from models.specs.version_manager import SchemaVersionManager

vm = SchemaVersionManager()

# Automatically migrate from v1.0.0 to v1.1.0
migrated_spec = vm.migrate_specification(old_spec, "v1.0.0", "v1.1.0")
```

### Manual Migration

For major version updates, provide migration utilities:

```python
from models.specs.migration import migrate_v1_to_v2

# Manual migration with user guidance
new_spec = migrate_v1_to_v2(old_spec, migration_options)
```

## Testing Requirements

### Version Compatibility Tests

1. **Forward Compatibility**: Ensure older specifications validate against newer minor versions
2. **Backward Compatibility**: Ensure newer specifications (without new features) validate against older versions
3. **Migration Tests**: Verify migration utilities work correctly

### Test Structure

```python
def test_version_compatibility():
    """Test compatibility between versions."""
    vm = SchemaVersionManager()
    
    # Test that v1.0.0 spec validates against v1.1.0
    assert vm.is_compatible("v1.0.0", "v1.1.0")
    
    # Test that v1.x.x is not compatible with v2.x.x
    assert not vm.is_compatible("v1.0.0", "v2.0.0")
```

## Documentation Requirements

When creating a new version:

1. **Update README.md** with new features
2. **Create CHANGELOG.md** entry
3. **Update API documentation**
4. **Provide migration guide** for breaking changes
5. **Update example specifications**

## Release Process

### 1. Pre-Release Checklist

- [ ] All tests pass
- [ ] Documentation updated
- [ ] Examples validated
- [ ] Migration utilities tested (if applicable)
- [ ] Backward compatibility verified

### 2. Release Steps

1. **Tag the release**:
   ```bash
   git tag -a v1.1.0 -m "Release version 1.1.0"
   ```

2. **Update version references** in code and documentation

3. **Deploy to production** environments

4. **Announce the release** with changelog

### 3. Post-Release

1. **Monitor for issues**
2. **Prepare patch releases** if needed
3. **Plan next version** features

## Schema Validation Levels

### Level 1: JSON Schema Validation
- Basic structure and type checking
- Required field validation
- Format validation (e.g., version strings)

### Level 2: Pydantic Model Validation
- Advanced type checking
- Custom validation logic
- Cross-field validation

### Level 3: Business Logic Validation
- Domain-specific rules
- Complex constraint checking
- Integration validation

## Best Practices

1. **Always validate** specifications against their declared schema version
2. **Use version manager** for all schema operations
3. **Test thoroughly** before releasing new versions
4. **Document breaking changes** clearly
5. **Provide migration paths** for major version updates
6. **Keep examples up-to-date** with latest schema versions
7. **Monitor usage** to understand impact of changes

## Emergency Procedures

### Critical Bug Fixes

1. **Assess impact** of the bug
2. **Create patch release** immediately
3. **Test thoroughly** despite urgency
4. **Deploy with rollback plan**
5. **Communicate** to all stakeholders

### Schema Corruption

1. **Identify** the scope of corruption
2. **Restore** from known good version
3. **Validate** all affected specifications
4. **Implement** additional safeguards

This version control system ensures the schema evolves safely while maintaining compatibility and providing clear upgrade paths for users of the Blender AI Agent system.
