#!/usr/bin/env python3
"""
Demo script for Specification Generation Agent

This script demonstrates the capabilities of the SpecGenerationAgent including:
- Rule-based specification generation from image analysis results
- Schema validation and compliance
- Integration with knowledge agent
- Error handling and confidence scoring

Author: Augment Agent
Date: 2025-07-17
"""

import json
import os
from pathlib import Path

# Import project modules
from agents.spec_generation_agent import (
    SpecGenerationAgent, SpecGenerationConfig, ValidationLevel
)
from agents.image_analysis_agent import (
    ImageAnalysisResult, DetectedShape, ShapeType, BoundingBox, 
    ColorInfo, AnalysisGranularity
)
from agents.knowledge_agent import KnowledgeAgent


def print_separator(title: str):
    """Print a formatted separator."""
    print("\n" + "=" * 80)
    print(f" {title}")
    print("=" * 80)


def create_sample_analysis_results():
    """Create sample image analysis results for demonstration."""
    
    # Sample 1: Single red cube
    cube_bbox = BoundingBox(x=0.3, y=0.2, width=0.4, height=0.4)
    cube_color = ColorInfo(r=1.0, g=0.0, b=0.0, dominant_color_name="red")
    cube_shape = DetectedShape(
        shape_type=ShapeType.CUBE,
        confidence=0.95,
        bounding_box=cube_bbox,
        color_info=cube_color,
        size_estimate={"relative_size": "large", "approximate_scale": 1.5}
    )
    
    single_cube_result = ImageAnalysisResult(
        image_path="demo_images/red_cube.png",
        detected_shapes=[cube_shape],
        overall_confidence=0.95,
        analysis_granularity=AnalysisGranularity.DETAILED,
        scene_description="A large red cube positioned in the center of the image"
    )
    
    # Sample 2: Multiple shapes
    sphere_bbox = BoundingBox(x=0.1, y=0.1, width=0.3, height=0.3)
    sphere_color = ColorInfo(r=0.0, g=1.0, b=0.0, dominant_color_name="green")
    sphere_shape = DetectedShape(
        shape_type=ShapeType.SPHERE,
        confidence=0.88,
        bounding_box=sphere_bbox,
        color_info=sphere_color,
        size_estimate={"relative_size": "medium", "approximate_scale": 1.0}
    )
    
    cylinder_bbox = BoundingBox(x=0.6, y=0.5, width=0.2, height=0.4)
    cylinder_color = ColorInfo(r=0.0, g=0.0, b=1.0, dominant_color_name="blue")
    cylinder_shape = DetectedShape(
        shape_type=ShapeType.CYLINDER,
        confidence=0.82,
        bounding_box=cylinder_bbox,
        color_info=cylinder_color,
        size_estimate={"relative_size": "small", "approximate_scale": 0.8}
    )
    
    multi_shapes_result = ImageAnalysisResult(
        image_path="demo_images/multi_shapes.png",
        detected_shapes=[sphere_shape, cylinder_shape],
        overall_confidence=0.85,
        analysis_granularity=AnalysisGranularity.ADVANCED,
        scene_description="A green sphere and blue cylinder arranged in the scene"
    )
    
    # Sample 3: Unknown shape (for error handling demo)
    unknown_bbox = BoundingBox(x=0.4, y=0.3, width=0.2, height=0.3)
    unknown_shape = DetectedShape(
        shape_type=ShapeType.UNKNOWN,
        confidence=0.45,
        bounding_box=unknown_bbox,
        color_info=None,
        size_estimate=None
    )
    
    unknown_result = ImageAnalysisResult(
        image_path="demo_images/unknown_shape.png",
        detected_shapes=[unknown_shape],
        overall_confidence=0.45,
        analysis_granularity=AnalysisGranularity.BASIC,
        scene_description="Unclear shape detected with low confidence"
    )
    
    return {
        "single_cube": single_cube_result,
        "multi_shapes": multi_shapes_result,
        "unknown_shape": unknown_result
    }


def demo_basic_generation():
    """Demonstrate basic specification generation."""
    print_separator("DEMO: Basic Specification Generation")
    
    # Create agent with default configuration
    agent = SpecGenerationAgent()
    
    # Get sample analysis results
    samples = create_sample_analysis_results()
    
    print("\n1. Generating specification for single red cube:")
    result = agent.generate_specification(samples["single_cube"])
    
    print(f"   ✅ Generation successful: {result.validation_passed}")
    print(f"   📊 Confidence score: {result.confidence_score:.2f}")
    print(f"   ⏱️  Generation time: {result.generation_time:.3f}s")
    print(f"   🔧 Objects created: {len(result.specification.get('objects', []))}")
    
    if result.validation_passed:
        obj = result.specification["objects"][0]
        print(f"   📦 Object type: {obj['geometry']['type']}")
        print(f"   🎨 Material color: {obj.get('material', {}).get('color', 'N/A')}")
    
    print(f"\n   Generated specification preview:")
    print(f"   Model name: {result.specification['model_info']['name']}")
    print(f"   Description: {result.specification['model_info']['description']}")
    print(f"   Tags: {result.specification['model_info']['tags']}")


def demo_multiple_shapes():
    """Demonstrate generation with multiple shapes."""
    print_separator("DEMO: Multiple Shapes Generation")
    
    agent = SpecGenerationAgent()
    samples = create_sample_analysis_results()
    
    print("\n2. Generating specification for multiple shapes:")
    result = agent.generate_specification(samples["multi_shapes"])
    
    print(f"   ✅ Generation successful: {result.validation_passed}")
    print(f"   📊 Confidence score: {result.confidence_score:.2f}")
    print(f"   🔧 Objects created: {len(result.specification.get('objects', []))}")
    
    if result.validation_passed:
        for i, obj in enumerate(result.specification["objects"]):
            print(f"   📦 Object {i+1}: {obj['geometry']['type']} ({obj['name']})")
            if 'material' in obj and 'color' in obj['material']:
                color = obj['material']['color']
                print(f"      🎨 Color: R={color['r']}, G={color['g']}, B={color['b']}")


def demo_validation_levels():
    """Demonstrate different validation levels."""
    print_separator("DEMO: Validation Levels")
    
    samples = create_sample_analysis_results()
    
    validation_levels = [
        (ValidationLevel.BASIC, "Basic structure validation"),
        (ValidationLevel.PYDANTIC, "Pydantic model validation"),
        (ValidationLevel.FULL, "Full validation (all levels)")
    ]
    
    print("\n3. Testing different validation levels:")
    
    for level, description in validation_levels:
        config = SpecGenerationConfig(validation_level=level)
        agent = SpecGenerationAgent(config=config)
        
        result = agent.generate_specification(samples["single_cube"])
        
        status = "✅ PASS" if result.validation_passed else "❌ FAIL"
        error_count = len(result.validation_errors)
        
        print(f"   {status} {description}")
        print(f"      Errors: {error_count}")
        if error_count > 0:
            print(f"      First error: {result.validation_errors[0]}")


def demo_error_handling():
    """Demonstrate error handling capabilities."""
    print_separator("DEMO: Error Handling")
    
    agent = SpecGenerationAgent()
    samples = create_sample_analysis_results()
    
    print("\n4. Testing error handling scenarios:")
    
    # Test with unknown shape
    print("   📝 Testing unknown shape handling:")
    result = agent.generate_specification(samples["unknown_shape"])
    
    print(f"      ✅ Handled gracefully: {result.validation_passed}")
    print(f"      📊 Confidence score: {result.confidence_score:.2f}")
    if result.validation_passed:
        obj = result.specification["objects"][0]
        print(f"      📦 Fallback object type: {obj['geometry']['type']}")
    
    # Test with empty shapes
    print("\n   📝 Testing empty shapes handling:")
    from agents.image_analysis_agent import ImageAnalysisResult, AnalysisGranularity
    
    empty_result = ImageAnalysisResult(
        image_path="empty.png",
        detected_shapes=[],
        overall_confidence=0.1,
        analysis_granularity=AnalysisGranularity.BASIC,
        scene_description="No clear shapes detected"
    )
    
    result = agent.generate_specification(empty_result)
    print(f"      ✅ Created default object: {result.validation_passed}")
    if result.validation_passed:
        print(f"      📦 Default object count: {len(result.specification['objects'])}")


def demo_user_preferences():
    """Demonstrate user preferences integration."""
    print_separator("DEMO: User Preferences Integration")
    
    agent = SpecGenerationAgent()
    samples = create_sample_analysis_results()
    
    print("\n5. Testing user preferences integration:")
    
    user_prefs = {
        "units": "centimeters",
        "preferred_material": "metallic",
        "scale_factor": 2.0
    }
    
    result = agent.generate_specification(
        samples["single_cube"],
        user_preferences=user_prefs,
        model_name="Custom Red Cube Model"
    )
    
    print(f"   ✅ Generation with preferences: {result.validation_passed}")
    print(f"   📏 Units: {result.specification['scene_settings']['units']}")
    print(f"   📛 Model name: {result.specification['model_info']['name']}")


def demo_performance_metrics():
    """Demonstrate performance metrics collection."""
    print_separator("DEMO: Performance Metrics")
    
    agent = SpecGenerationAgent()
    samples = create_sample_analysis_results()
    
    print("\n6. Performance metrics collection:")
    
    # Run multiple generations to get average timing
    times = []
    for i in range(5):
        result = agent.generate_specification(samples["single_cube"])
        times.append(result.generation_time)
    
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"   ⏱️  Average generation time: {avg_time:.3f}s")
    print(f"   ⚡ Fastest generation: {min_time:.3f}s")
    print(f"   🐌 Slowest generation: {max_time:.3f}s")
    
    # Show metadata
    print(f"\n   📊 Metadata from last generation:")
    for key, value in result.metadata.items():
        print(f"      {key}: {value}")


def main():
    """Run all demonstrations."""
    print("🚀 Specification Generation Agent Demo")
    print("=" * 80)
    print("This demo showcases the capabilities of the SpecGenerationAgent")
    print("for generating 3D model specifications from image analysis results.")
    
    try:
        demo_basic_generation()
        demo_multiple_shapes()
        demo_validation_levels()
        demo_error_handling()
        demo_user_preferences()
        demo_performance_metrics()
        
        print_separator("DEMO COMPLETED SUCCESSFULLY")
        print("\n✅ All demonstrations completed successfully!")
        print("📋 The SpecGenerationAgent is ready for production use.")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
