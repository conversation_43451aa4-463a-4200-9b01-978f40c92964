# Knowledge Retrieval Evaluation Metrics

## Overview

This document defines the evaluation metrics and methodologies used to assess the performance of the Knowledge Agent's retrieval system. The Knowledge Agent uses a RAG (Retrieval-Augmented Generation) approach with vector embeddings to provide contextual information about Blender Python API, MCP (Molecular Nodes), and 3D modeling best practices.

## Evaluation Framework

### 1. Core Metrics

#### 1.1 Retrieval Accuracy
**Definition**: The percentage of queries that return at least one relevant result in the top-K results.

**Formula**: 
```
Accuracy = (Number of queries with relevant results) / (Total number of queries)
```

**Target**: >80% for predefined basic API queries
**Measurement**: Binary relevance assessment based on topic matching

#### 1.2 Average Relevance Score
**Definition**: The mean relevance score of the top-ranked result across all test queries.

**Formula**:
```
Average Relevance Score = Σ(relevance_score_top_result) / (Total number of queries)
```

**Range**: 0.0 to 1.0 (where 1.0 is perfect relevance)
**Target**: >0.7 for high-quality retrieval

#### 1.3 Top-K Precision
**Definition**: The proportion of relevant documents in the top-K retrieved results.

**Formula**:
```
Precision@K = (Number of relevant documents in top-K) / K
```

**Evaluation**: Measured for K = 1, 3, 5
**Target**: Precision@5 > 0.6

#### 1.4 Mean Reciprocal Rank (MRR)
**Definition**: The average of reciprocal ranks of the first relevant result.

**Formula**:
```
MRR = (1/|Q|) * Σ(1/rank_i)
```

Where rank_i is the position of the first relevant result for query i.

**Target**: MRR > 0.8

### 2. Specialized Metrics

#### 2.1 Source Distribution Accuracy
**Definition**: How well the retrieval system identifies the correct knowledge source for different query types.

**Categories**:
- Blender API queries → BLENDER_API source
- MCP queries → MCP_DOCS source
- General modeling → BEST_PRACTICES source

**Target**: >90% correct source identification

#### 2.2 API Function Retrieval Precision
**Definition**: For Blender API function queries, the percentage that return the exact function documentation.

**Test Cases**:
- `bpy.ops.mesh.primitive_cube_add`
- `bpy.ops.mesh.primitive_uv_sphere_add`
- `bpy.data.materials.new`
- `bpy.context.object`
- `bpy.ops.export_scene.obj`

**Target**: 100% exact match for direct API queries

#### 2.3 Semantic Understanding Score
**Definition**: Ability to retrieve relevant results for natural language queries about Blender operations.

**Test Queries**:
- "How to create a cube in Blender?"
- "Add material to object"
- "Export 3D model as OBJ"
- "Delete default objects"
- "Create molecular structure"

**Evaluation**: Manual relevance assessment on 1-5 scale
**Target**: Average score > 4.0

## Test Dataset

### 3.1 Predefined Test Queries

#### Basic API Queries (Exact Match Expected)
```python
BASIC_API_QUERIES = [
    {
        "query": "bpy.ops.mesh.primitive_cube_add",
        "expected_topics": ["bpy.ops.mesh.primitive_cube_add"],
        "source": "BLENDER_API",
        "type": "exact_api"
    },
    {
        "query": "bpy.ops.mesh.primitive_uv_sphere_add",
        "expected_topics": ["bpy.ops.mesh.primitive_uv_sphere_add"],
        "source": "BLENDER_API",
        "type": "exact_api"
    },
    {
        "query": "bpy.data.materials.new",
        "expected_topics": ["bpy.data.materials.new"],
        "source": "BLENDER_API",
        "type": "exact_api"
    }
]
```

#### Semantic Queries (Conceptual Match Expected)
```python
SEMANTIC_QUERIES = [
    {
        "query": "create cube primitive",
        "expected_topics": ["bpy.ops.mesh.primitive_cube_add", "cube", "primitive"],
        "source": "BLENDER_API",
        "type": "semantic"
    },
    {
        "query": "add sphere to scene",
        "expected_topics": ["bpy.ops.mesh.primitive_uv_sphere_add", "sphere"],
        "source": "BLENDER_API",
        "type": "semantic"
    },
    {
        "query": "material creation",
        "expected_topics": ["bpy.data.materials.new", "material"],
        "source": "BLENDER_API",
        "type": "semantic"
    }
]
```

#### MCP-Specific Queries
```python
MCP_QUERIES = [
    {
        "query": "molecular nodes atom creation",
        "expected_topics": ["atom", "molecular", "MCP"],
        "source": "MCP_DOCS",
        "type": "mcp"
    },
    {
        "query": "protein structure import",
        "expected_topics": ["protein", "import", "PDB"],
        "source": "MCP_DOCS",
        "type": "mcp"
    }
]
```

### 3.2 Evaluation Methodology

#### Automated Evaluation
1. **Exact Match**: Query matches expected topic exactly
2. **Partial Match**: Query result contains expected keywords
3. **Source Validation**: Retrieved results come from expected source
4. **Relevance Scoring**: Distance-based relevance calculation

#### Manual Evaluation
1. **Content Quality**: Human assessment of result relevance (1-5 scale)
2. **Completeness**: Whether result provides sufficient information
3. **Accuracy**: Factual correctness of retrieved information

## Performance Benchmarks

### 4.1 Quantitative Targets

| Metric | Target | Minimum Acceptable |
|--------|--------|-------------------|
| Retrieval Accuracy | >80% | 70% |
| Average Relevance Score | >0.7 | 0.6 |
| Precision@5 | >0.6 | 0.5 |
| MRR | >0.8 | 0.7 |
| API Function Precision | 100% | 95% |
| Source Distribution Accuracy | >90% | 85% |

### 4.2 Performance Characteristics

#### Response Time Targets
- Single query: <500ms
- Batch queries (10): <2s
- Knowledge base loading: <30s

#### Scalability Metrics
- Support for 1000+ knowledge chunks
- Concurrent query handling: 10+ simultaneous queries
- Memory usage: <2GB for full knowledge base

## Evaluation Implementation

### 5.1 Test Execution

```python
def run_evaluation(knowledge_agent, test_queries):
    """Run comprehensive evaluation of knowledge retrieval."""
    results = {
        'accuracy': 0.0,
        'average_relevance': 0.0,
        'precision_at_k': {},
        'mrr': 0.0,
        'source_accuracy': 0.0,
        'response_times': []
    }
    
    # Execute test queries and collect metrics
    for query_data in test_queries:
        start_time = time.time()
        retrieval_results = knowledge_agent.query_knowledge(
            query_data['query'], 
            top_k=5
        )
        response_time = time.time() - start_time
        
        # Calculate metrics for this query
        # ... metric calculation logic
    
    return results
```

### 5.2 Continuous Monitoring

#### Real-time Metrics
- Query success rate
- Average response time
- Error rate
- Cache hit rate

#### Periodic Evaluation
- Weekly automated evaluation runs
- Monthly manual quality assessment
- Quarterly knowledge base updates

## Quality Assurance

### 6.1 Knowledge Base Quality
- Regular content audits
- Version control for documentation updates
- Consistency checks across sources
- Duplicate detection and removal

### 6.2 Embedding Quality
- Embedding model performance monitoring
- Semantic similarity validation
- Cross-validation with different embedding models
- Regular re-embedding for updated content

### 6.3 Retrieval System Health
- Vector database performance monitoring
- Index optimization
- Query pattern analysis
- Failure mode identification

## Reporting and Visualization

### 7.1 Evaluation Reports
- Automated daily performance summaries
- Weekly detailed analysis reports
- Monthly trend analysis
- Quarterly comprehensive reviews

### 7.2 Metrics Dashboard
- Real-time performance indicators
- Historical trend visualization
- Query pattern analysis
- Error rate monitoring

### 7.3 Alert System
- Performance degradation alerts
- Error rate threshold alerts
- Knowledge base staleness warnings
- System health notifications

## Future Enhancements

### 8.1 Advanced Metrics
- Semantic coherence scoring
- Multi-hop reasoning evaluation
- Context-aware relevance assessment
- User satisfaction scoring

### 8.2 Evaluation Automation
- Automated test case generation
- Continuous integration testing
- A/B testing framework
- Performance regression detection

### 8.3 Knowledge Base Evolution
- Dynamic content updates
- User feedback integration
- Community contribution system
- Multi-language support evaluation

## Conclusion

This evaluation framework provides comprehensive metrics for assessing the Knowledge Agent's retrieval performance. Regular monitoring and evaluation ensure that the system maintains high-quality knowledge retrieval capabilities, supporting the broader Blender 3D model generation AI Agent system with accurate and relevant contextual information.

The combination of automated and manual evaluation methods, along with continuous monitoring, ensures that the Knowledge Agent meets the quantitative standards defined in the project requirements while maintaining high-quality user experience.
