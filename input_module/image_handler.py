"""
Image Handler Module for Blender 3D Model Generation AI Agent

This module provides comprehensive image input and preprocessing capabilities including:
- Local image loading with format validation
- Image standardization and normalization
- AI image generation API integration (DALL-E)
- Robust error handling and retry mechanisms
- Input validation and graceful degradation
"""

import os
import io
import time
import logging
from typing import Optional, Tuple, Dict, Any, Union
from pathlib import Path
import requests
from PIL import Image, ImageOps
import openai
from openai import OpenAI
import base64
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageSource(Enum):
    """Enumeration of supported image sources"""
    LOCAL = "local"
    URL = "url"
    AI_GENERATED = "ai_generated"

class ImageFormat(Enum):
    """Supported image formats"""
    PNG = "PNG"
    JPEG = "JPEG"
    JPG = "JPEG"  # Alias for JPEG
    BMP = "BMP"

@dataclass
class ImageMetadata:
    """Metadata for processed images"""
    source: ImageSource
    original_path: Optional[str]
    format: str
    size: Tuple[int, int]
    processed_path: str
    ai_generation_params: Optional[Dict[str, Any]] = None

class ImageProcessingError(Exception):
    """Custom exception for image processing errors"""
    pass

class APIError(Exception):
    """Custom exception for API-related errors"""
    pass

class ImageHandler:
    """
    Core image processing class for handling various image input sources
    and preprocessing operations with robust error handling.
    """
    
    SUPPORTED_FORMATS = {'.png', '.jpg', '.jpeg', '.bmp'}
    DEFAULT_SIZE = (256, 256)
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0  # seconds
    
    def __init__(self, 
                 output_dir: str = "processed_images",
                 default_size: Tuple[int, int] = DEFAULT_SIZE,
                 openai_api_key: Optional[str] = None):
        """
        Initialize ImageHandler with configuration options.
        
        Args:
            output_dir: Directory to save processed images
            default_size: Default size for image standardization
            openai_api_key: OpenAI API key for DALL-E integration
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.default_size = default_size
        
        # Initialize OpenAI client if API key is provided
        self.openai_client = None
        if openai_api_key:
            try:
                self.openai_client = OpenAI(api_key=openai_api_key)
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
    
    def process_image(self,
                     image_input: Union[str, Path, bytes],
                     source_type: ImageSource = ImageSource.LOCAL,
                     target_size: Optional[Tuple[int, int]] = None,
                     ai_params: Optional[Dict[str, Any]] = None) -> ImageMetadata:
        """
        Process image from various sources with standardization.

        Args:
            image_input: Image path, URL, or bytes data
            source_type: Type of image source
            target_size: Target size for standardization
            ai_params: Parameters for AI image generation

        Returns:
            ImageMetadata: Metadata about the processed image

        Raises:
            ImageProcessingError: If image processing fails
            APIError: If API calls fail after retries
        """
        if target_size is None:
            target_size = self.default_size

        try:
            if source_type == ImageSource.LOCAL:
                return self._process_local_image(image_input, target_size)
            elif source_type == ImageSource.URL:
                return self._process_url_image(image_input, target_size)
            elif source_type == ImageSource.AI_GENERATED:
                return self._process_ai_generated_image(image_input, target_size, ai_params)
            else:
                raise ImageProcessingError(f"Unsupported source type: {source_type}")

        except (APIError, ImageProcessingError):
            # Re-raise API and ImageProcessing errors as-is
            raise
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            raise ImageProcessingError(f"Failed to process image: {str(e)}")
    
    def _process_local_image(self, image_path: Union[str, Path], target_size: Tuple[int, int]) -> ImageMetadata:
        """Process local image file with validation and standardization."""
        image_path = Path(image_path)
        
        # Validate file existence
        if not image_path.exists():
            raise ImageProcessingError(f"Image file not found: {image_path}")
        
        # Validate file format
        if image_path.suffix.lower() not in self.SUPPORTED_FORMATS:
            raise ImageProcessingError(f"Unsupported image format: {image_path.suffix}")
        
        try:
            # Load and process image
            with Image.open(image_path) as img:
                processed_img = self._standardize_image(img, target_size)
                
                # Save processed image
                output_filename = f"processed_{int(time.time())}_{image_path.name}"
                output_path = self.output_dir / output_filename
                processed_img.save(output_path)
                
                return ImageMetadata(
                    source=ImageSource.LOCAL,
                    original_path=str(image_path),
                    format=processed_img.format,
                    size=processed_img.size,
                    processed_path=str(output_path)
                )
                
        except Exception as e:
            raise ImageProcessingError(f"Failed to process local image {image_path}: {str(e)}")
    
    def _process_url_image(self, image_url: str, target_size: Tuple[int, int]) -> ImageMetadata:
        """Process image from URL with download and validation."""
        try:
            # Download image with retry mechanism
            response = self._download_with_retry(image_url)
            
            # Load image from bytes
            img_bytes = io.BytesIO(response.content)
            with Image.open(img_bytes) as img:
                processed_img = self._standardize_image(img, target_size)
                
                # Save processed image
                output_filename = f"url_processed_{int(time.time())}.png"
                output_path = self.output_dir / output_filename
                processed_img.save(output_path)
                
                return ImageMetadata(
                    source=ImageSource.URL,
                    original_path=image_url,
                    format=processed_img.format,
                    size=processed_img.size,
                    processed_path=str(output_path)
                )
                
        except Exception as e:
            raise ImageProcessingError(f"Failed to process URL image {image_url}: {str(e)}")
    
    def _process_ai_generated_image(self, 
                                  prompt: str, 
                                  target_size: Tuple[int, int],
                                  ai_params: Optional[Dict[str, Any]] = None) -> ImageMetadata:
        """Generate and process image using AI API (DALL-E)."""
        if not self.openai_client:
            raise APIError("OpenAI client not initialized. Please provide API key.")
        
        if ai_params is None:
            ai_params = {}
        
        try:
            # Generate image using DALL-E with retry mechanism
            image_url = self._generate_dalle_image_with_retry(prompt, ai_params)
            
            # Download and process the generated image
            response = self._download_with_retry(image_url)
            img_bytes = io.BytesIO(response.content)
            
            with Image.open(img_bytes) as img:
                processed_img = self._standardize_image(img, target_size)
                
                # Save processed image
                output_filename = f"ai_generated_{int(time.time())}.png"
                output_path = self.output_dir / output_filename
                processed_img.save(output_path)
                
                return ImageMetadata(
                    source=ImageSource.AI_GENERATED,
                    original_path=None,
                    format=processed_img.format,
                    size=processed_img.size,
                    processed_path=str(output_path),
                    ai_generation_params={
                        'prompt': prompt,
                        **ai_params
                    }
                )
                
        except Exception as e:
            raise APIError(f"Failed to generate AI image: {str(e)}")
    
    def _standardize_image(self, img: Image.Image, target_size: Tuple[int, int]) -> Image.Image:
        """Standardize image format, size, and properties."""
        # Convert to RGB if necessary
        if img.mode != 'RGB':
            img = img.convert('RGB')
        
        # Resize image while maintaining aspect ratio
        img = ImageOps.fit(img, target_size, Image.Resampling.LANCZOS)
        
        return img
    
    def _download_with_retry(self, url: str) -> requests.Response:
        """Download content from URL with retry mechanism."""
        for attempt in range(self.MAX_RETRIES):
            try:
                response = requests.get(url, timeout=30)
                response.raise_for_status()
                return response
            except requests.RequestException as e:
                if attempt == self.MAX_RETRIES - 1:
                    raise APIError(f"Failed to download from {url} after {self.MAX_RETRIES} attempts: {str(e)}")
                
                wait_time = self.RETRY_DELAY * (2 ** attempt)  # Exponential backoff
                logger.warning(f"Download attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
    
    def _generate_dalle_image_with_retry(self, prompt: str, params: Dict[str, Any]) -> str:
        """Generate image using DALL-E API with retry mechanism."""
        # Set default parameters
        dalle_params = {
            'model': 'dall-e-3',
            'prompt': prompt,
            'size': '1024x1024',
            'quality': 'standard',
            'n': 1,
            **params
        }
        
        for attempt in range(self.MAX_RETRIES):
            try:
                response = self.openai_client.images.generate(**dalle_params)
                return response.data[0].url
                
            except Exception as e:
                if attempt == self.MAX_RETRIES - 1:
                    raise APIError(f"DALL-E API failed after {self.MAX_RETRIES} attempts: {str(e)}")
                
                wait_time = self.RETRY_DELAY * (2 ** attempt)
                logger.warning(f"DALL-E attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
    
    def validate_image_format(self, file_path: Union[str, Path]) -> bool:
        """Validate if file format is supported."""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.SUPPORTED_FORMATS
    
    def get_image_info(self, image_path: Union[str, Path]) -> Dict[str, Any]:
        """Get basic information about an image file."""
        image_path = Path(image_path)
        
        if not image_path.exists():
            raise ImageProcessingError(f"Image file not found: {image_path}")
        
        try:
            with Image.open(image_path) as img:
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'filename': image_path.name,
                    'file_size': image_path.stat().st_size
                }
        except Exception as e:
            raise ImageProcessingError(f"Failed to get image info: {str(e)}")
