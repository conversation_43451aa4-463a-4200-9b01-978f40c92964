"""
Simplified Knowledge Agent for Blender 3D Model Generation AI Agent System

This is a simplified version of the Knowledge Agent that works without external dependencies
for demonstration and testing purposes. It provides basic text-based knowledge retrieval.

Author: Augment Agent
Date: 2025-07-17
"""

import os
import logging
import time
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeSource(Enum):
    """Enumeration of knowledge source types."""
    BLENDER_API = "blender_api"
    MCP_DOCS = "mcp_docs"
    BEST_PRACTICES = "best_practices"
    EXAMPLES = "examples"


@dataclass
class KnowledgeChunk:
    """Represents a chunk of knowledge with metadata."""
    id: str
    content: str
    source: KnowledgeSource
    topic: str
    subtopic: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class RetrievalResult:
    """Represents a knowledge retrieval result."""
    chunk: KnowledgeChunk
    relevance_score: float
    distance: float


class KnowledgeRetrievalError(Exception):
    """Exception raised for knowledge retrieval errors."""
    pass


class SimpleKnowledgeAgent:
    """
    Simplified Knowledge Agent for retrieving Blender API documentation and MCP examples.
    
    This version uses simple text-based search instead of vector embeddings for basic functionality.
    """
    
    def __init__(self, 
                 knowledge_base_path: str = "knowledge_base",
                 db_path: str = "simple_db"):
        """
        Initialize the Simple Knowledge Agent.
        
        Args:
            knowledge_base_path: Path to knowledge base files
            db_path: Path to simple database storage
        """
        self.knowledge_base_path = Path(knowledge_base_path)
        self.db_path = Path(db_path)
        self.db_path.mkdir(exist_ok=True)
        
        self.knowledge_chunks: List[KnowledgeChunk] = []
        self.loaded = False
        
    def load_knowledge_base(self, force_reload: bool = False) -> bool:
        """
        Load knowledge base from files.
        
        Args:
            force_reload: Force reload even if already loaded
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if already loaded
            if self.loaded and not force_reload:
                logger.info(f"Knowledge base already loaded with {len(self.knowledge_chunks)} chunks")
                return True
            
            # Clear existing data if force reload
            if force_reload:
                self.knowledge_chunks = []
            
            # Load knowledge chunks from files
            self._load_knowledge_chunks()
            
            if not self.knowledge_chunks:
                logger.warning("No knowledge chunks loaded")
                return False
            
            # Save to simple database
            self._save_to_simple_db()
            
            self.loaded = True
            logger.info(f"Successfully loaded {len(self.knowledge_chunks)} knowledge chunks")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")
            raise KnowledgeRetrievalError(f"Knowledge base loading failed: {e}")
    
    def _load_knowledge_chunks(self):
        """Load knowledge chunks from text files."""
        self.knowledge_chunks = []
        
        # Load from blender_docs_subset.txt
        blender_docs_path = self.knowledge_base_path / "blender_docs_subset.txt"
        if blender_docs_path.exists():
            self._parse_knowledge_file(blender_docs_path, KnowledgeSource.BLENDER_API)
        else:
            logger.warning(f"Blender docs file not found: {blender_docs_path}")
        
        # Load additional knowledge files if they exist
        for source_type in KnowledgeSource:
            if source_type == KnowledgeSource.BLENDER_API:
                continue  # Already loaded above
                
            filename = f"{source_type.value}_docs.txt"
            file_path = self.knowledge_base_path / filename
            if file_path.exists():
                self._parse_knowledge_file(file_path, source_type)
    
    def _parse_knowledge_file(self, file_path: Path, source: KnowledgeSource):
        """Parse a knowledge file and extract chunks."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split content into chunks
            chunks = self._split_into_chunks(content)
            
            for i, chunk_content in enumerate(chunks):
                if chunk_content.strip():
                    chunk_id = f"{source.value}_{file_path.stem}_{i}"
                    topic = self._extract_topic(chunk_content)
                    
                    chunk = KnowledgeChunk(
                        id=chunk_id,
                        content=chunk_content.strip(),
                        source=source,
                        topic=topic,
                        metadata={"file": str(file_path)}
                    )
                    self.knowledge_chunks.append(chunk)
                    
        except Exception as e:
            logger.error(f"Failed to parse knowledge file {file_path}: {e}")
    
    def _split_into_chunks(self, content: str, max_chunk_size: int = 1000) -> List[str]:
        """Split content into manageable chunks."""
        # Simple chunking by paragraphs and size
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _extract_topic(self, content: str) -> str:
        """Extract topic from content (simple heuristic)."""
        lines = content.strip().split('\n')
        first_line = lines[0].strip()
        
        # Look for common patterns
        if first_line.startswith('#'):
            return first_line.strip('#').strip()
        elif 'bpy.' in first_line:
            return first_line.split('bpy.')[1].split('(')[0] if '(' in first_line else first_line.split('bpy.')[1]
        elif len(first_line) < 100:
            return first_line
        else:
            return "general"
    
    def _save_to_simple_db(self):
        """Save chunks to simple JSON database."""
        db_file = self.db_path / "knowledge_chunks.json"
        
        chunks_data = []
        for chunk in self.knowledge_chunks:
            chunk_data = {
                "id": chunk.id,
                "content": chunk.content,
                "source": chunk.source.value,
                "topic": chunk.topic,
                "subtopic": chunk.subtopic,
                "metadata": chunk.metadata
            }
            chunks_data.append(chunk_data)
        
        with open(db_file, 'w', encoding='utf-8') as f:
            json.dump(chunks_data, f, indent=2, ensure_ascii=False)
    
    def _load_from_simple_db(self):
        """Load chunks from simple JSON database."""
        db_file = self.db_path / "knowledge_chunks.json"
        
        if not db_file.exists():
            return False
        
        try:
            with open(db_file, 'r', encoding='utf-8') as f:
                chunks_data = json.load(f)
            
            self.knowledge_chunks = []
            for chunk_data in chunks_data:
                chunk = KnowledgeChunk(
                    id=chunk_data["id"],
                    content=chunk_data["content"],
                    source=KnowledgeSource(chunk_data["source"]),
                    topic=chunk_data["topic"],
                    subtopic=chunk_data.get("subtopic"),
                    metadata=chunk_data.get("metadata")
                )
                self.knowledge_chunks.append(chunk)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load from simple database: {e}")
            return False
    
    def query_knowledge(self, 
                       query: str, 
                       top_k: int = 5,
                       source_filter: Optional[KnowledgeSource] = None) -> List[RetrievalResult]:
        """
        Query the knowledge base for relevant information using simple text search.
        
        Args:
            query: Search query
            top_k: Number of top results to return
            source_filter: Filter by knowledge source type
            
        Returns:
            List of retrieval results sorted by relevance
        """
        try:
            if not self.loaded:
                # Try to load from simple database
                if not self._load_from_simple_db():
                    logger.warning("Knowledge base not loaded. Load knowledge base first.")
                    return []
                self.loaded = True
            
            if not self.knowledge_chunks:
                logger.warning("No knowledge chunks available.")
                return []
            
            # Filter by source if specified
            chunks_to_search = self.knowledge_chunks
            if source_filter:
                chunks_to_search = [chunk for chunk in chunks_to_search if chunk.source == source_filter]
            
            # Simple text-based search
            query_lower = query.lower()
            scored_results = []
            
            for chunk in chunks_to_search:
                score = self._calculate_text_similarity(query_lower, chunk)
                if score > 0:
                    result = RetrievalResult(
                        chunk=chunk,
                        relevance_score=score,
                        distance=1.0 - score
                    )
                    scored_results.append(result)
            
            # Sort by relevance score (descending)
            scored_results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            # Return top-k results
            results = scored_results[:top_k]
            
            logger.info(f"Retrieved {len(results)} results for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Knowledge query failed: {e}")
            raise KnowledgeRetrievalError(f"Query execution failed: {e}")
    
    def _calculate_text_similarity(self, query: str, chunk: KnowledgeChunk) -> float:
        """Calculate simple text similarity score."""
        content_lower = chunk.content.lower()
        topic_lower = chunk.topic.lower()
        
        # Split query into words
        query_words = set(query.split())
        content_words = set(content_lower.split())
        topic_words = set(topic_lower.split())
        
        # Calculate word overlap scores
        content_overlap = len(query_words.intersection(content_words)) / len(query_words) if query_words else 0
        topic_overlap = len(query_words.intersection(topic_words)) / len(query_words) if query_words else 0
        
        # Check for exact phrase matches
        phrase_match = 1.0 if query in content_lower else 0.0
        topic_phrase_match = 1.0 if query in topic_lower else 0.0
        
        # Weighted combination
        score = (
            phrase_match * 0.4 +
            topic_phrase_match * 0.3 +
            content_overlap * 0.2 +
            topic_overlap * 0.1
        )
        
        return min(score, 1.0)
    
    def get_blender_api_docs(self, function_name: str) -> List[RetrievalResult]:
        """Get Blender API documentation for a specific function."""
        query = f"bpy.{function_name}" if not function_name.startswith('bpy.') else function_name
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.BLENDER_API
        )
    
    def get_mcp_examples(self, model_type: str) -> List[RetrievalResult]:
        """Get MCP (Molecular Nodes) examples for a specific model type."""
        query = f"molecular nodes {model_type}"
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.MCP_DOCS
        )
    
    def get_knowledge_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        try:
            if not self.loaded:
                self._load_from_simple_db()
                self.loaded = True
            
            total_chunks = len(self.knowledge_chunks)
            
            # Get source distribution
            source_stats = {}
            for source in KnowledgeSource:
                count = len([chunk for chunk in self.knowledge_chunks if chunk.source == source])
                source_stats[source.value] = count
            
            return {
                "total_chunks": total_chunks,
                "source_distribution": source_stats,
                "embedding_model": "text-based",
                "has_embeddings": False
            }
        except Exception as e:
            logger.error(f"Failed to get knowledge stats: {e}")
            return {"error": str(e)}
    
    def evaluate_retrieval_quality(self, test_queries: List[Dict[str, Any]]) -> Dict[str, float]:
        """Evaluate retrieval quality using test queries."""
        if not test_queries:
            return {"error": "No test queries provided"}
        
        total_queries = len(test_queries)
        correct_retrievals = 0
        total_relevance_score = 0.0
        
        for test_query in test_queries:
            query = test_query['query']
            expected_topics = set(test_query.get('expected_topics', []))
            
            results = self.query_knowledge(query, top_k=5)
            
            if results:
                # Check if any result matches expected topics
                retrieved_topics = {result.chunk.topic for result in results}
                retrieved_content = {result.chunk.content.lower() for result in results}
                
                # Check for topic match or content match
                topic_match = bool(expected_topics.intersection(retrieved_topics))
                content_match = any(
                    any(expected.lower() in content for expected in expected_topics)
                    for content in retrieved_content
                )
                
                if topic_match or content_match:
                    correct_retrievals += 1
                
                # Calculate average relevance score for top result
                total_relevance_score += results[0].relevance_score
        
        accuracy = correct_retrievals / total_queries if total_queries > 0 else 0.0
        avg_relevance = total_relevance_score / total_queries if total_queries > 0 else 0.0
        
        return {
            "accuracy": accuracy,
            "average_relevance_score": avg_relevance,
            "total_queries": total_queries,
            "correct_retrievals": correct_retrievals
        }
