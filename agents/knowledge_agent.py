"""
Knowledge Agent for Blender 3D Model Generation AI Agent System

This module implements a knowledge retrieval agent that provides access to Blender Python API
documentation, MCP (Molecular Nodes) usage examples, and 3D modeling best practices through
a vector database-powered RAG (Retrieval-Augmented Generation) system.

Author: Augment Agent
Date: 2025-07-17
"""

import os
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

import chromadb
from chromadb.config import Settings
import openai
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class KnowledgeSource(Enum):
    """Enumeration of knowledge source types."""
    BLENDER_API = "blender_api"
    MCP_DOCS = "mcp_docs"
    BEST_PRACTICES = "best_practices"
    EXAMPLES = "examples"


@dataclass
class KnowledgeChunk:
    """Represents a chunk of knowledge with metadata."""
    id: str
    content: str
    source: KnowledgeSource
    topic: str
    subtopic: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class RetrievalResult:
    """Represents a knowledge retrieval result."""
    chunk: KnowledgeChunk
    relevance_score: float
    distance: float


class KnowledgeRetrievalError(Exception):
    """Exception raised for knowledge retrieval errors."""
    pass


class KnowledgeAgent:
    """
    Knowledge Agent for retrieving Blender API documentation and MCP examples.
    
    This agent uses ChromaDB for vector storage and OpenAI embeddings for semantic search.
    It provides context-aware knowledge retrieval to support other agents in the system.
    """
    
    def __init__(self, 
                 knowledge_base_path: str = "knowledge_base",
                 db_path: str = "chroma_db",
                 openai_api_key: Optional[str] = None,
                 embedding_model: str = "text-embedding-3-small"):
        """
        Initialize the Knowledge Agent.
        
        Args:
            knowledge_base_path: Path to knowledge base files
            db_path: Path to ChromaDB storage
            openai_api_key: OpenAI API key for embeddings
            embedding_model: OpenAI embedding model to use
        """
        self.knowledge_base_path = Path(knowledge_base_path)
        self.db_path = Path(db_path)
        self.embedding_model = embedding_model
        
        # Initialize OpenAI client
        api_key = openai_api_key or os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.warning("No OpenAI API key provided. Some functionality may be limited.")
            self.openai_client = None
        else:
            self.openai_client = OpenAI(api_key=api_key)
        
        # Initialize ChromaDB
        self.chroma_client = chromadb.PersistentClient(
            path=str(self.db_path),
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Create or get collection
        self.collection = self.chroma_client.get_or_create_collection(
            name="blender_knowledge",
            metadata={"description": "Blender API and MCP knowledge base"}
        )
        
        self.knowledge_chunks: List[KnowledgeChunk] = []
        
    def load_knowledge_base(self, force_reload: bool = False) -> bool:
        """
        Load knowledge base from files and create embeddings.
        
        Args:
            force_reload: Force reload even if collection already has data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if collection already has data
            if not force_reload and self.collection.count() > 0:
                logger.info(f"Knowledge base already loaded with {self.collection.count()} chunks")
                return True
            
            # Clear existing data if force reload
            if force_reload:
                self.chroma_client.delete_collection("blender_knowledge")
                self.collection = self.chroma_client.create_collection(
                    name="blender_knowledge",
                    metadata={"description": "Blender API and MCP knowledge base"}
                )
            
            # Load knowledge chunks from files
            self._load_knowledge_chunks()
            
            if not self.knowledge_chunks:
                logger.warning("No knowledge chunks loaded")
                return False
            
            # Generate embeddings and store in vector database
            self._store_embeddings()
            
            logger.info(f"Successfully loaded {len(self.knowledge_chunks)} knowledge chunks")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load knowledge base: {e}")
            raise KnowledgeRetrievalError(f"Knowledge base loading failed: {e}")
    
    def _load_knowledge_chunks(self):
        """Load knowledge chunks from text files."""
        self.knowledge_chunks = []
        
        # Load from blender_docs_subset.txt
        blender_docs_path = self.knowledge_base_path / "blender_docs_subset.txt"
        if blender_docs_path.exists():
            self._parse_knowledge_file(blender_docs_path, KnowledgeSource.BLENDER_API)
        else:
            logger.warning(f"Blender docs file not found: {blender_docs_path}")
        
        # Load additional knowledge files if they exist
        for source_type in KnowledgeSource:
            if source_type == KnowledgeSource.BLENDER_API:
                continue  # Already loaded above
                
            filename = f"{source_type.value}_docs.txt"
            file_path = self.knowledge_base_path / filename
            if file_path.exists():
                self._parse_knowledge_file(file_path, source_type)
    
    def _parse_knowledge_file(self, file_path: Path, source: KnowledgeSource):
        """Parse a knowledge file and extract chunks."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Split content into chunks (simple approach - can be enhanced)
            chunks = self._split_into_chunks(content)
            
            for i, chunk_content in enumerate(chunks):
                if chunk_content.strip():
                    chunk_id = f"{source.value}_{file_path.stem}_{i}"
                    topic = self._extract_topic(chunk_content)
                    
                    chunk = KnowledgeChunk(
                        id=chunk_id,
                        content=chunk_content.strip(),
                        source=source,
                        topic=topic,
                        metadata={"file": str(file_path)}
                    )
                    self.knowledge_chunks.append(chunk)
                    
        except Exception as e:
            logger.error(f"Failed to parse knowledge file {file_path}: {e}")
    
    def _split_into_chunks(self, content: str, max_chunk_size: int = 1000) -> List[str]:
        """Split content into manageable chunks."""
        # Simple chunking by paragraphs and size
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        if current_chunk:
            chunks.append(current_chunk)
        
        return chunks
    
    def _extract_topic(self, content: str) -> str:
        """Extract topic from content (simple heuristic)."""
        lines = content.strip().split('\n')
        first_line = lines[0].strip()
        
        # Look for common patterns
        if first_line.startswith('#'):
            return first_line.strip('#').strip()
        elif 'bpy.' in first_line:
            return first_line.split('bpy.')[1].split('(')[0] if '(' in first_line else first_line.split('bpy.')[1]
        elif len(first_line) < 100:
            return first_line
        else:
            return "general"
    
    def _store_embeddings(self):
        """Generate embeddings and store in ChromaDB."""
        if not self.openai_client:
            logger.warning("No OpenAI client available for embeddings")
            # Store without embeddings for testing
            self._store_without_embeddings()
            return
        
        batch_size = 100
        for i in range(0, len(self.knowledge_chunks), batch_size):
            batch = self.knowledge_chunks[i:i + batch_size]
            self._process_embedding_batch(batch)
    
    def _process_embedding_batch(self, batch: List[KnowledgeChunk]):
        """Process a batch of chunks for embedding."""
        try:
            # Prepare texts for embedding
            texts = [chunk.content for chunk in batch]
            
            # Generate embeddings
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=texts
            )
            
            # Prepare data for ChromaDB
            ids = [chunk.id for chunk in batch]
            embeddings = [data.embedding for data in response.data]
            documents = texts
            metadatas = [
                {
                    "source": chunk.source.value,
                    "topic": chunk.topic,
                    "subtopic": chunk.subtopic or "",
                    **(chunk.metadata or {})
                }
                for chunk in batch
            ]
            
            # Add to collection
            self.collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"Processed embedding batch of {len(batch)} chunks")
            
        except Exception as e:
            logger.error(f"Failed to process embedding batch: {e}")
            raise KnowledgeRetrievalError(f"Embedding generation failed: {e}")
    
    def _store_without_embeddings(self):
        """Store chunks without embeddings (for testing)."""
        ids = [chunk.id for chunk in self.knowledge_chunks]
        documents = [chunk.content for chunk in self.knowledge_chunks]
        metadatas = [
            {
                "source": chunk.source.value,
                "topic": chunk.topic,
                "subtopic": chunk.subtopic or "",
                **(chunk.metadata or {})
            }
            for chunk in self.knowledge_chunks
        ]
        
        self.collection.add(
            ids=ids,
            documents=documents,
            metadatas=metadatas
        )

    def query_knowledge(self,
                       query: str,
                       top_k: int = 5,
                       source_filter: Optional[KnowledgeSource] = None) -> List[RetrievalResult]:
        """
        Query the knowledge base for relevant information.

        Args:
            query: Search query
            top_k: Number of top results to return
            source_filter: Filter by knowledge source type

        Returns:
            List of retrieval results sorted by relevance
        """
        try:
            if self.collection.count() == 0:
                logger.warning("Knowledge base is empty. Load knowledge base first.")
                return []

            # Prepare query filters
            where_filter = {}
            if source_filter:
                where_filter["source"] = source_filter.value

            if self.openai_client:
                # Generate query embedding
                query_embedding = self._generate_query_embedding(query)

                # Perform vector search
                results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k,
                    where=where_filter if where_filter else None
                )
            else:
                # Fallback to text-based search
                results = self.collection.query(
                    query_texts=[query],
                    n_results=top_k,
                    where=where_filter if where_filter else None
                )

            # Convert to RetrievalResult objects
            retrieval_results = []
            for i in range(len(results['ids'][0])):
                chunk = KnowledgeChunk(
                    id=results['ids'][0][i],
                    content=results['documents'][0][i],
                    source=KnowledgeSource(results['metadatas'][0][i]['source']),
                    topic=results['metadatas'][0][i]['topic'],
                    subtopic=results['metadatas'][0][i].get('subtopic'),
                    metadata=results['metadatas'][0][i]
                )

                distance = results['distances'][0][i] if 'distances' in results else 0.0
                relevance_score = 1.0 - distance  # Convert distance to relevance

                retrieval_results.append(RetrievalResult(
                    chunk=chunk,
                    relevance_score=relevance_score,
                    distance=distance
                ))

            logger.info(f"Retrieved {len(retrieval_results)} results for query: {query[:50]}...")
            return retrieval_results

        except Exception as e:
            logger.error(f"Knowledge query failed: {e}")
            raise KnowledgeRetrievalError(f"Query execution failed: {e}")

    def _generate_query_embedding(self, query: str) -> List[float]:
        """Generate embedding for query text."""
        try:
            response = self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=query
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate query embedding: {e}")
            raise KnowledgeRetrievalError(f"Query embedding failed: {e}")

    def get_blender_api_docs(self, function_name: str) -> List[RetrievalResult]:
        """
        Get Blender API documentation for a specific function.

        Args:
            function_name: Name of the Blender function/API

        Returns:
            List of relevant documentation results
        """
        query = f"bpy.{function_name}" if not function_name.startswith('bpy.') else function_name
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.BLENDER_API
        )

    def get_mcp_examples(self, model_type: str) -> List[RetrievalResult]:
        """
        Get MCP (Molecular Nodes) examples for a specific model type.

        Args:
            model_type: Type of molecular model

        Returns:
            List of relevant MCP examples
        """
        query = f"molecular nodes {model_type}"
        return self.query_knowledge(
            query=query,
            top_k=3,
            source_filter=KnowledgeSource.MCP_DOCS
        )

    def get_knowledge_stats(self) -> Dict[str, Any]:
        """Get statistics about the knowledge base."""
        try:
            total_chunks = self.collection.count()

            # Get source distribution
            source_stats = {}
            for source in KnowledgeSource:
                count = len(self.collection.get(where={"source": source.value})['ids'])
                source_stats[source.value] = count

            return {
                "total_chunks": total_chunks,
                "source_distribution": source_stats,
                "embedding_model": self.embedding_model,
                "has_embeddings": self.openai_client is not None
            }
        except Exception as e:
            logger.error(f"Failed to get knowledge stats: {e}")
            return {"error": str(e)}

    def evaluate_retrieval_quality(self, test_queries: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        Evaluate retrieval quality using test queries.

        Args:
            test_queries: List of test queries with expected results

        Returns:
            Dictionary of evaluation metrics
        """
        if not test_queries:
            return {"error": "No test queries provided"}

        total_queries = len(test_queries)
        correct_retrievals = 0
        total_relevance_score = 0.0

        for test_query in test_queries:
            query = test_query['query']
            expected_topics = set(test_query.get('expected_topics', []))

            results = self.query_knowledge(query, top_k=5)

            if results:
                # Check if any result matches expected topics
                retrieved_topics = {result.chunk.topic for result in results}
                if expected_topics.intersection(retrieved_topics):
                    correct_retrievals += 1

                # Calculate average relevance score for top result
                total_relevance_score += results[0].relevance_score

        accuracy = correct_retrievals / total_queries if total_queries > 0 else 0.0
        avg_relevance = total_relevance_score / total_queries if total_queries > 0 else 0.0

        return {
            "accuracy": accuracy,
            "average_relevance_score": avg_relevance,
            "total_queries": total_queries,
            "correct_retrievals": correct_retrievals
        }
