"""
Image Analysis Agent for Blender 3D Model Generation AI Agent

This module provides comprehensive image analysis capabilities including:
- Basic geometric shape recognition (cube, sphere, cylinder)
- Multi-modal LLM integration for high-level intent inference
- Confidence scoring and structured output
- Robust error handling and validation
"""

import os
import json
import logging
import base64
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
import openai
from openai import OpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ShapeType(Enum):
    """Supported basic geometric shapes."""
    CUBE = "cube"
    SPHERE = "sphere"
    CYLINDER = "cylinder"
    CONE = "cone"
    PLANE = "plane"
    UNKNOWN = "unknown"


class AnalysisGranularity(Enum):
    """Analysis granularity levels."""
    BASIC = "basic"          # Basic shape recognition only
    DETAILED = "detailed"    # Shape + position + color
    ADVANCED = "advanced"    # Full scene understanding


@dataclass
class BoundingBox:
    """Bounding box coordinates."""
    x: float
    y: float
    width: float
    height: float
    
    def to_dict(self) -> Dict[str, float]:
        return asdict(self)


@dataclass
class ColorInfo:
    """Color information."""
    r: float  # Red component (0-1)
    g: float  # Green component (0-1)
    b: float  # Blue component (0-1)
    dominant_color_name: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class DetectedShape:
    """Detected shape information."""
    shape_type: ShapeType
    confidence: float
    bounding_box: Optional[BoundingBox] = None
    color_info: Optional[ColorInfo] = None
    size_estimate: Optional[Dict[str, float]] = None  # Relative size information
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            'shape_type': self.shape_type.value,
            'confidence': self.confidence
        }
        if self.bounding_box:
            result['bounding_box'] = self.bounding_box.to_dict()
        if self.color_info:
            result['color_info'] = self.color_info.to_dict()
        if self.size_estimate:
            result['size_estimate'] = self.size_estimate
        return result


@dataclass
class ImageAnalysisResult:
    """Complete image analysis result."""
    image_path: str
    detected_shapes: List[DetectedShape]
    overall_confidence: float
    analysis_granularity: AnalysisGranularity
    scene_description: Optional[str] = None
    processing_time: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'image_path': self.image_path,
            'detected_shapes': [shape.to_dict() for shape in self.detected_shapes],
            'overall_confidence': self.overall_confidence,
            'analysis_granularity': self.analysis_granularity.value,
            'scene_description': self.scene_description,
            'processing_time': self.processing_time,
            'metadata': self.metadata or {}
        }


class ImageAnalysisError(Exception):
    """Custom exception for image analysis errors."""
    pass


class ImageAnalysisAgent:
    """
    Image Analysis Agent for 3D model generation.
    
    Uses computer vision and multi-modal LLM to analyze images and extract
    3D model-relevant features including basic geometric shapes, colors,
    and spatial relationships.
    """
    
    # Vision model configuration
    VISION_MODEL = "gpt-4o"  # GPT-4 with vision capabilities
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0
    
    # Shape recognition prompts
    SHAPE_ANALYSIS_PROMPT = """
    Analyze this image and identify any basic 3D geometric shapes present.
    Focus on identifying: cubes, spheres, cylinders, cones, and planes.
    
    For each shape you detect, provide:
    1. Shape type (cube, sphere, cylinder, cone, plane, or unknown)
    2. Confidence level (0.0 to 1.0)
    3. Approximate position in the image (as percentage of image dimensions)
    4. Dominant color (RGB values 0-1 and color name)
    5. Relative size compared to other objects
    
    Respond in JSON format with this structure:
    {
        "detected_shapes": [
            {
                "shape_type": "cube",
                "confidence": 0.95,
                "bounding_box": {"x": 0.2, "y": 0.3, "width": 0.4, "height": 0.4},
                "color_info": {"r": 1.0, "g": 0.0, "b": 0.0, "dominant_color_name": "red"},
                "size_estimate": {"relative_size": "medium", "approximate_scale": 1.0}
            }
        ],
        "scene_description": "A red cube positioned in the center of the image",
        "overall_confidence": 0.95
    }
    """
    
    def __init__(self, openai_api_key: Optional[str] = None):
        """
        Initialize ImageAnalysisAgent.
        
        Args:
            openai_api_key: OpenAI API key for vision model access
        """
        self.openai_client = None
        if openai_api_key:
            try:
                self.openai_client = OpenAI(api_key=openai_api_key)
            except Exception as e:
                logger.warning(f"Failed to initialize OpenAI client: {e}")
        
        # Initialize supported shape types
        self.supported_shapes = {shape.value for shape in ShapeType if shape != ShapeType.UNKNOWN}
    
    def analyze_image(self, 
                     image_path: Union[str, Path],
                     analysis_granularity: AnalysisGranularity = AnalysisGranularity.BASIC) -> ImageAnalysisResult:
        """
        Analyze image to detect basic geometric shapes.
        
        Args:
            image_path: Path to the image file
            analysis_granularity: Level of analysis detail
            
        Returns:
            ImageAnalysisResult: Structured analysis results
            
        Raises:
            ImageAnalysisError: If analysis fails
        """
        import time
        start_time = time.time()
        
        try:
            # Validate input
            image_path = Path(image_path)
            if not image_path.exists():
                raise ImageAnalysisError(f"Image file not found: {image_path}")
            
            # Analyze image using vision model
            analysis_result = self._analyze_with_vision_model(image_path, analysis_granularity)
            
            # Calculate processing time
            processing_time = time.time() - start_time
            analysis_result.processing_time = processing_time
            
            logger.info(f"Image analysis completed in {processing_time:.2f}s for {image_path}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Image analysis failed for {image_path}: {e}")
            raise ImageAnalysisError(f"Failed to analyze image: {str(e)}")
    
    def _analyze_with_vision_model(self, 
                                  image_path: Path,
                                  granularity: AnalysisGranularity) -> ImageAnalysisResult:
        """Analyze image using OpenAI vision model."""
        if not self.openai_client:
            raise ImageAnalysisError("OpenAI client not initialized. Please provide API key.")
        
        try:
            # Encode image to base64
            image_base64 = self._encode_image_to_base64(image_path)
            
            # Prepare messages for vision model
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": self.SHAPE_ANALYSIS_PROMPT},
                        {
                            "type": "image_url",
                            "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}
                        }
                    ]
                }
            ]
            
            # Call vision model with retry mechanism
            response = self._call_vision_model_with_retry(messages)
            
            # Parse response
            return self._parse_vision_response(response, str(image_path), granularity)
            
        except Exception as e:
            raise ImageAnalysisError(f"Vision model analysis failed: {str(e)}")
    
    def _encode_image_to_base64(self, image_path: Path) -> str:
        """Encode image to base64 string."""
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise ImageAnalysisError(f"Failed to encode image: {str(e)}")
    
    def _call_vision_model_with_retry(self, messages: List[Dict]) -> str:
        """Call vision model with retry mechanism."""
        import time
        
        for attempt in range(self.MAX_RETRIES):
            try:
                response = self.openai_client.chat.completions.create(
                    model=self.VISION_MODEL,
                    messages=messages,
                    max_tokens=1000,
                    temperature=0.1
                )
                return response.choices[0].message.content
                
            except Exception as e:
                if attempt == self.MAX_RETRIES - 1:
                    raise ImageAnalysisError(f"Vision model failed after {self.MAX_RETRIES} attempts: {str(e)}")
                
                wait_time = self.RETRY_DELAY * (2 ** attempt)
                logger.warning(f"Vision model attempt {attempt + 1} failed, retrying in {wait_time}s: {e}")
                time.sleep(wait_time)
    
    def _parse_vision_response(self, 
                              response: str,
                              image_path: str,
                              granularity: AnalysisGranularity) -> ImageAnalysisResult:
        """Parse vision model response into structured result."""
        try:
            # Extract JSON from response
            response_data = self._extract_json_from_response(response)
            
            # Parse detected shapes
            detected_shapes = []
            for shape_data in response_data.get('detected_shapes', []):
                shape = self._parse_detected_shape(shape_data)
                if shape:
                    detected_shapes.append(shape)
            
            # Calculate overall confidence
            overall_confidence = response_data.get('overall_confidence', 0.0)
            if detected_shapes and overall_confidence == 0.0:
                overall_confidence = sum(shape.confidence for shape in detected_shapes) / len(detected_shapes)
            
            return ImageAnalysisResult(
                image_path=image_path,
                detected_shapes=detected_shapes,
                overall_confidence=overall_confidence,
                analysis_granularity=granularity,
                scene_description=response_data.get('scene_description'),
                metadata={'raw_response': response_data}
            )
            
        except Exception as e:
            logger.error(f"Failed to parse vision response: {e}")
            # Return empty result with low confidence
            return ImageAnalysisResult(
                image_path=image_path,
                detected_shapes=[],
                overall_confidence=0.0,
                analysis_granularity=granularity,
                scene_description="Failed to parse analysis results",
                metadata={'error': str(e), 'raw_response': response}
            )
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """Extract JSON data from model response."""
        try:
            # Try to parse the entire response as JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # Try to find JSON block in the response
            import re
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # Try to find any JSON-like structure
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            
            raise ValueError("No valid JSON found in response")
    
    def _parse_detected_shape(self, shape_data: Dict[str, Any]) -> Optional[DetectedShape]:
        """Parse individual detected shape data."""
        try:
            # Parse shape type
            shape_type_str = shape_data.get('shape_type', 'unknown').lower()
            shape_type = ShapeType.UNKNOWN
            for st in ShapeType:
                if st.value == shape_type_str:
                    shape_type = st
                    break
            
            # Parse confidence
            confidence = float(shape_data.get('confidence', 0.0))
            confidence = max(0.0, min(1.0, confidence))  # Clamp to [0, 1]
            
            # Parse bounding box
            bounding_box = None
            if 'bounding_box' in shape_data:
                bbox_data = shape_data['bounding_box']
                bounding_box = BoundingBox(
                    x=float(bbox_data.get('x', 0.0)),
                    y=float(bbox_data.get('y', 0.0)),
                    width=float(bbox_data.get('width', 0.0)),
                    height=float(bbox_data.get('height', 0.0))
                )
            
            # Parse color info
            color_info = None
            if 'color_info' in shape_data:
                color_data = shape_data['color_info']
                color_info = ColorInfo(
                    r=float(color_data.get('r', 0.0)),
                    g=float(color_data.get('g', 0.0)),
                    b=float(color_data.get('b', 0.0)),
                    dominant_color_name=color_data.get('dominant_color_name')
                )
            
            # Parse size estimate
            size_estimate = shape_data.get('size_estimate')
            
            return DetectedShape(
                shape_type=shape_type,
                confidence=confidence,
                bounding_box=bounding_box,
                color_info=color_info,
                size_estimate=size_estimate
            )
            
        except Exception as e:
            logger.warning(f"Failed to parse detected shape: {e}")
            return None
    
    def get_supported_shapes(self) -> List[str]:
        """Get list of supported shape types."""
        return list(self.supported_shapes)
    
    def validate_analysis_result(self, result: ImageAnalysisResult) -> bool:
        """Validate analysis result structure and content."""
        try:
            # Check basic structure
            if not isinstance(result.detected_shapes, list):
                return False
            
            if not (0.0 <= result.overall_confidence <= 1.0):
                return False
            
            # Validate each detected shape
            for shape in result.detected_shapes:
                if not isinstance(shape, DetectedShape):
                    return False
                if not (0.0 <= shape.confidence <= 1.0):
                    return False
                if shape.shape_type not in ShapeType:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Result validation failed: {e}")
            return False
