"""
Unit tests for BlenderExecutor module.

This module contains comprehensive tests for the BlenderExecutor class,
including success scenarios, error handling, and output parsing.

Author: AI Agent System
Date: 2025-07-17
"""

import os
import pytest
import tempfile
import subprocess
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the project root to the path
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from blender_interface.blender_executor import (
    BlenderExecutor,
    BlenderOutput,
    BlenderExecutionStatus,
    BlenderExecutionError
)


class TestBlenderExecutor:
    """Test suite for BlenderExecutor class."""
    
    @pytest.fixture
    def mock_blender_path(self):
        """Fixture providing a mock Blender path."""
        return "/usr/bin/blender"
    
    @pytest.fixture
    def executor(self, mock_blender_path):
        """Fixture providing a BlenderExecutor instance with mocked Blender path."""
        with patch('os.path.exists', return_value=True), \
             patch('os.access', return_value=True):
            return BlenderExecutor(blender_path=mock_blender_path)
    
    @pytest.fixture
    def sample_script(self):
        """Fixture providing a sample Blender script."""
        return """
import bpy

# Clear existing mesh objects
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete(use_global=False)

# Create a cube
bpy.ops.mesh.primitive_cube_add(size=2.0, location=(0, 0, 0))

# Save the file
bpy.ops.wm.save_as_mainfile(filepath="/tmp/test_cube.blend")
print("Successfully created cube and saved to /tmp/test_cube.blend")
"""
    
    @pytest.fixture
    def error_script(self):
        """Fixture providing a script that will cause errors."""
        return """
import bpy

# This will cause a NameError
undefined_variable.some_method()

# This will cause a TypeError
bpy.ops.mesh.primitive_cube_add(invalid_parameter="invalid")
"""
    
    def test_init_with_valid_path(self, mock_blender_path):
        """Test BlenderExecutor initialization with valid Blender path."""
        with patch('os.path.exists', return_value=True), \
             patch('os.access', return_value=True):
            executor = BlenderExecutor(blender_path=mock_blender_path)
            assert executor.blender_path == mock_blender_path
            assert executor.timeout == 300
    
    def test_init_with_invalid_path(self):
        """Test BlenderExecutor initialization with invalid Blender path."""
        with patch('os.path.exists', return_value=False):
            with pytest.raises(BlenderExecutionError, match="Blender executable not found"):
                BlenderExecutor(blender_path="/invalid/path")
    
    def test_init_with_environment_variable(self):
        """Test BlenderExecutor initialization using environment variable."""
        with patch.dict(os.environ, {'BLENDER_PATH': '/env/blender'}), \
             patch('os.path.exists', return_value=True), \
             patch('os.access', return_value=True):
            executor = BlenderExecutor()
            assert executor.blender_path == '/env/blender'
    
    def test_init_without_blender_path(self):
        """Test BlenderExecutor initialization without Blender path."""
        with patch.dict(os.environ, {}, clear=True), \
             patch('os.path.exists', return_value=False):
            with pytest.raises(BlenderExecutionError, match="Blender executable not found"):
                BlenderExecutor()
    
    @patch('subprocess.run')
    def test_execute_script_success(self, mock_subprocess, executor, sample_script):
        """Test successful script execution."""
        # Mock successful subprocess execution
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = "Successfully created cube and saved to /tmp/test_cube.blend\nBlender quit"
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result
        
        # Execute script
        output = executor.execute_script(sample_script)
        
        # Verify results
        assert output.status == BlenderExecutionStatus.SUCCESS
        assert output.return_code == 0
        assert "Successfully created cube" in output.stdout
        assert output.stderr == ""
        assert len(output.output_files) > 0
        assert output.error_details is None
    
    @patch('subprocess.run')
    def test_execute_script_error(self, mock_subprocess, executor, error_script):
        """Test script execution with errors."""
        # Mock failed subprocess execution
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stdout = "Blender started"
        mock_result.stderr = """
Traceback (most recent call last):
  File "/tmp/script.py", line 4, in <module>
    undefined_variable.some_method()
NameError: name 'undefined_variable' is not defined
"""
        mock_subprocess.return_value = mock_result
        
        # Execute script
        output = executor.execute_script(error_script)
        
        # Verify results
        assert output.status == BlenderExecutionStatus.SCRIPT_ERROR
        assert output.return_code == 1
        assert "NameError" in output.stderr
        assert output.error_details is not None
        assert "name_error" in output.error_details
    
    @patch('subprocess.run')
    def test_execute_script_timeout(self, mock_subprocess, executor, sample_script):
        """Test script execution timeout."""
        # Mock timeout exception
        mock_subprocess.side_effect = subprocess.TimeoutExpired("blender", 300)
        
        # Execute script
        output = executor.execute_script(sample_script)
        
        # Verify results
        assert output.status == BlenderExecutionStatus.TIMEOUT
        assert output.return_code == -1
        assert "timed out" in output.stderr
        assert output.error_details is not None
        assert "timeout" in output.error_details
    
    @patch('subprocess.run')
    def test_execute_script_unexpected_error(self, mock_subprocess, executor, sample_script):
        """Test script execution with unexpected error."""
        # Mock unexpected exception
        mock_subprocess.side_effect = Exception("Unexpected error")
        
        # Execute script
        output = executor.execute_script(sample_script)
        
        # Verify results
        assert output.status == BlenderExecutionStatus.ERROR
        assert output.return_code == -1
        assert "Unexpected error" in output.stderr
        assert output.error_details is not None
        assert "exception" in output.error_details
    
    def test_parse_error_details_syntax_error(self, executor):
        """Test parsing of syntax error details."""
        stderr = """
  File "/tmp/script.py", line 3
    invalid syntax here
                      ^
SyntaxError: invalid syntax
"""
        error_details = executor._parse_error_details(stderr, "")
        assert "syntax_error" in error_details
        assert "invalid syntax" in error_details["syntax_error"]
    
    def test_parse_error_details_name_error(self, executor):
        """Test parsing of name error details."""
        stderr = "NameError: name 'undefined_variable' is not defined"
        error_details = executor._parse_error_details(stderr, "")
        assert "name_error" in error_details
        assert "undefined_variable" in error_details["name_error"]
    
    def test_parse_error_details_attribute_error(self, executor):
        """Test parsing of attribute error details."""
        stderr = "AttributeError: 'NoneType' object has no attribute 'location'"
        error_details = executor._parse_error_details(stderr, "")
        assert "attribute_error" in error_details
        assert "NoneType" in error_details["attribute_error"]
    
    def test_parse_error_details_traceback(self, executor):
        """Test parsing of full traceback."""
        stderr = """
Traceback (most recent call last):
  File "/tmp/script.py", line 4, in <module>
    undefined_variable.some_method()
NameError: name 'undefined_variable' is not defined
"""
        error_details = executor._parse_error_details(stderr, "")
        assert "traceback" in error_details
        assert "File \"/tmp/script.py\"" in error_details["traceback"]
    
    def test_find_output_files_from_stdout(self, executor):
        """Test finding output files from stdout messages."""
        stdout = """
Blender started
Successfully saved Blender file to /tmp/test_cube.blend
Saved '/tmp/output.obj'
Written: /tmp/render.png
Exported: /tmp/model.fbx
"""
        output_files = executor._find_output_files(stdout, None)
        assert len(output_files) >= 3
        assert any("/tmp/test_cube.blend" in f for f in output_files)
        assert any("/tmp/output.obj" in f for f in output_files)
    
    @patch('os.path.exists')
    @patch('os.listdir')
    def test_find_output_files_from_directory(self, mock_listdir, mock_exists, executor):
        """Test finding output files from output directory."""
        mock_exists.return_value = True
        mock_listdir.return_value = ['test.blend', 'render.png', 'model.obj', 'readme.txt']
        
        output_files = executor._find_output_files("", "/tmp/output")
        
        # Should find files with common 3D/image extensions
        assert len(output_files) == 3  # .blend, .png, .obj (not .txt)
        assert any("test.blend" in f for f in output_files)
        assert any("render.png" in f for f in output_files)
        assert any("model.obj" in f for f in output_files)
    
    def test_parse_additional_info_object_creation(self, executor):
        """Test parsing of object creation information."""
        stdout = """
Created 5 objects
Created 3 meshes
Created 2 materials
Render completed
"""
        info = executor._parse_additional_info(stdout)
        assert info["objects_created"] == 5
        assert info["meshes_created"] == 3
        assert info["materials_created"] == 2
    
    def test_parse_additional_info_render_info(self, executor):
        """Test parsing of render information."""
        stdout = """
Time: 02:34.56
Sample 128/256
Mem:1024.5M
"""
        info = executor._parse_additional_info(stdout)
        assert "render_time" in info
        assert "samples" in info
        assert "memory_usage" in info
    
    def test_execute_script_file_success(self, executor):
        """Test executing script from file."""
        script_content = "import bpy\nprint('Hello from Blender')"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(script_content)
            temp_file_path = temp_file.name
        
        try:
            with patch.object(executor, 'execute_script') as mock_execute:
                mock_output = BlenderOutput(
                    status=BlenderExecutionStatus.SUCCESS,
                    stdout="Hello from Blender",
                    stderr="",
                    return_code=0,
                    execution_time=1.0,
                    output_files=[]
                )
                mock_execute.return_value = mock_output
                
                result = executor.execute_script_file(temp_file_path)
                
                assert result.status == BlenderExecutionStatus.SUCCESS
                mock_execute.assert_called_once_with(script_content, None)
        
        finally:
            os.unlink(temp_file_path)
    
    def test_execute_script_file_not_found(self, executor):
        """Test executing non-existent script file."""
        with pytest.raises(BlenderExecutionError, match="Script file not found"):
            executor.execute_script_file("/nonexistent/script.py")
    
    @patch('subprocess.run')
    def test_get_blender_version_success(self, mock_subprocess, executor):
        """Test getting Blender version successfully."""
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stdout = "Blender 3.6.0 (hash 2023-06-27)"
        mock_subprocess.return_value = mock_result
        
        version = executor.get_blender_version()
        assert version == "3.6.0"
    
    @patch('subprocess.run')
    def test_get_blender_version_failure(self, mock_subprocess, executor):
        """Test getting Blender version with failure."""
        mock_subprocess.side_effect = Exception("Command failed")
        
        version = executor.get_blender_version()
        assert version == "Unknown"
    
    def test_blender_output_dataclass(self):
        """Test BlenderOutput dataclass functionality."""
        output = BlenderOutput(
            status=BlenderExecutionStatus.SUCCESS,
            stdout="test output",
            stderr="",
            return_code=0,
            execution_time=1.5,
            output_files=["/tmp/test.blend"],
            error_details=None,
            parsed_info={"objects_created": 1}
        )
        
        assert output.status == BlenderExecutionStatus.SUCCESS
        assert output.stdout == "test output"
        assert output.execution_time == 1.5
        assert len(output.output_files) == 1
        assert output.parsed_info["objects_created"] == 1
    
    def test_blender_execution_error(self):
        """Test BlenderExecutionError exception."""
        output = BlenderOutput(
            status=BlenderExecutionStatus.ERROR,
            stdout="",
            stderr="Test error",
            return_code=1,
            execution_time=0.5,
            output_files=[]
        )
        
        error = BlenderExecutionError("Test error message", output)
        assert str(error) == "Test error message"
        assert error.output == output


if __name__ == "__main__":
    pytest.main([__file__])
