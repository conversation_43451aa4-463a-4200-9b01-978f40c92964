"""
Test suite for Knowledge Agent

This module contains comprehensive unit tests for the Knowledge Agent,
covering knowledge base loading, text embedding, vector storage, and semantic retrieval.

Author: Augment Agent
Date: 2025-07-17
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import os

from agents.knowledge_agent import (
    KnowledgeAgent, 
    KnowledgeSource, 
    KnowledgeChunk, 
    RetrievalResult,
    KnowledgeRetrievalError
)


class TestKnowledgeAgent:
    """Test suite for KnowledgeAgent class."""
    
    @pytest.fixture
    def temp_dirs(self):
        """Create temporary directories for testing."""
        temp_dir = tempfile.mkdtemp()
        knowledge_base_dir = Path(temp_dir) / "knowledge_base"
        db_dir = Path(temp_dir) / "chroma_db"
        
        knowledge_base_dir.mkdir(parents=True)
        
        yield {
            "temp_dir": temp_dir,
            "knowledge_base": str(knowledge_base_dir),
            "db": str(db_dir)
        }
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    @pytest.fixture
    def sample_knowledge_content(self):
        """Sample knowledge base content for testing."""
        return """# Blender API Documentation

## bpy.ops.mesh.primitive_cube_add
Creates a cube mesh primitive in the current scene.

Parameters:
- size: float, default 2.0
- location: Vector, default (0, 0, 0)

Example:
```python
import bpy
bpy.ops.mesh.primitive_cube_add(size=2.0)
```

## bpy.ops.mesh.primitive_uv_sphere_add
Creates a UV sphere mesh primitive.

Parameters:
- radius: float, default 1.0
- location: Vector, default (0, 0, 0)

Example:
```python
import bpy
bpy.ops.mesh.primitive_uv_sphere_add(radius=1.0)
```

## Material System
Basic material creation and assignment.

### bpy.data.materials.new
Creates a new material with specified name.

Example:
```python
material = bpy.data.materials.new(name="MyMaterial")
material.diffuse_color = (1.0, 0.0, 0.0, 1.0)
```
"""
    
    @pytest.fixture
    def mock_openai_client(self):
        """Mock OpenAI client for testing."""
        mock_client = Mock()
        
        # Mock embedding response
        mock_embedding_response = Mock()
        mock_embedding_response.data = [Mock(embedding=[0.1, 0.2, 0.3] * 512)]  # 1536 dimensions
        mock_client.embeddings.create.return_value = mock_embedding_response
        
        return mock_client
    
    def test_knowledge_agent_initialization(self, temp_dirs):
        """Test KnowledgeAgent initialization."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"],
            openai_api_key="test_key"
        )
        
        assert agent.knowledge_base_path == Path(temp_dirs["knowledge_base"])
        assert agent.db_path == Path(temp_dirs["db"])
        assert agent.embedding_model == "text-embedding-3-small"
        assert agent.openai_client is not None
        assert agent.collection is not None
    
    def test_knowledge_agent_initialization_no_api_key(self, temp_dirs):
        """Test KnowledgeAgent initialization without API key."""
        with patch.dict(os.environ, {}, clear=True):
            agent = KnowledgeAgent(
                knowledge_base_path=temp_dirs["knowledge_base"],
                db_path=temp_dirs["db"]
            )
            
            assert agent.openai_client is None
    
    def test_load_knowledge_base_success(self, temp_dirs, sample_knowledge_content, mock_openai_client):
        """Test successful knowledge base loading."""
        # Create sample knowledge file
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        with patch('agents.knowledge_agent.OpenAI', return_value=mock_openai_client):
            agent = KnowledgeAgent(
                knowledge_base_path=temp_dirs["knowledge_base"],
                db_path=temp_dirs["db"],
                openai_api_key="test_key"
            )
            
            result = agent.load_knowledge_base()
            
            assert result is True
            assert len(agent.knowledge_chunks) > 0
            assert agent.collection.count() > 0
    
    def test_load_knowledge_base_no_files(self, temp_dirs, mock_openai_client):
        """Test knowledge base loading with no files."""
        with patch('agents.knowledge_agent.OpenAI', return_value=mock_openai_client):
            agent = KnowledgeAgent(
                knowledge_base_path=temp_dirs["knowledge_base"],
                db_path=temp_dirs["db"],
                openai_api_key="test_key"
            )
            
            result = agent.load_knowledge_base()
            
            assert result is False
            assert len(agent.knowledge_chunks) == 0
    
    def test_parse_knowledge_file(self, temp_dirs, sample_knowledge_content):
        """Test knowledge file parsing."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent._parse_knowledge_file(knowledge_file, KnowledgeSource.BLENDER_API)
        
        assert len(agent.knowledge_chunks) > 0
        
        # Check that chunks have correct properties
        for chunk in agent.knowledge_chunks:
            assert isinstance(chunk, KnowledgeChunk)
            assert chunk.source == KnowledgeSource.BLENDER_API
            assert chunk.content.strip() != ""
            assert chunk.topic != ""
    
    def test_split_into_chunks(self, temp_dirs):
        """Test content splitting into chunks."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        content = "Short paragraph.\n\n" + "Long paragraph. " * 200
        chunks = agent._split_into_chunks(content, max_chunk_size=500)
        
        assert len(chunks) > 1
        for chunk in chunks:
            assert len(chunk) <= 600  # Some tolerance for splitting logic
    
    def test_extract_topic(self, temp_dirs):
        """Test topic extraction from content."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        # Test different content patterns
        test_cases = [
            ("# Blender API", "Blender API"),
            ("bpy.ops.mesh.primitive_cube_add", "ops.mesh.primitive_cube_add"),
            ("Short title", "Short title"),
            ("Very long content that should be truncated because it's too long for a topic", "general")
        ]
        
        for content, expected in test_cases:
            topic = agent._extract_topic(content)
            if expected == "general":
                assert topic == "general" or len(topic) < 100
            else:
                assert expected in topic or topic == expected
    
    @patch('agents.knowledge_agent.OpenAI')
    def test_store_embeddings_with_openai(self, mock_openai_class, temp_dirs, sample_knowledge_content):
        """Test embedding storage with OpenAI client."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [Mock(embedding=[0.1] * 1536) for _ in range(5)]
        mock_client.embeddings.create.return_value = mock_response
        mock_openai_class.return_value = mock_client
        
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"],
            openai_api_key="test_key"
        )
        
        agent._load_knowledge_chunks()
        agent._store_embeddings()
        
        assert mock_client.embeddings.create.called
        assert agent.collection.count() > 0
    
    def test_store_without_embeddings(self, temp_dirs, sample_knowledge_content):
        """Test storage without embeddings."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent._load_knowledge_chunks()
        agent._store_without_embeddings()
        
        assert agent.collection.count() > 0
    
    def test_query_knowledge_empty_database(self, temp_dirs):
        """Test querying empty knowledge base."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        results = agent.query_knowledge("test query")
        
        assert results == []
    
    @patch('agents.knowledge_agent.OpenAI')
    def test_query_knowledge_with_embeddings(self, mock_openai_class, temp_dirs, sample_knowledge_content):
        """Test knowledge querying with embeddings."""
        mock_client = Mock()
        
        # Mock embedding creation
        mock_embedding_response = Mock()
        mock_embedding_response.data = [Mock(embedding=[0.1] * 1536)]
        mock_client.embeddings.create.return_value = mock_embedding_response
        
        mock_openai_class.return_value = mock_client
        
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"],
            openai_api_key="test_key"
        )
        
        agent.load_knowledge_base()
        results = agent.query_knowledge("cube primitive", top_k=3)
        
        assert isinstance(results, list)
        for result in results:
            assert isinstance(result, RetrievalResult)
            assert isinstance(result.chunk, KnowledgeChunk)
            assert 0 <= result.relevance_score <= 1
    
    def test_query_knowledge_without_embeddings(self, temp_dirs, sample_knowledge_content):
        """Test knowledge querying without embeddings (text-based)."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent.load_knowledge_base()
        results = agent.query_knowledge("cube", top_k=2)
        
        assert isinstance(results, list)
        assert len(results) <= 2
    
    def test_get_blender_api_docs(self, temp_dirs, sample_knowledge_content):
        """Test getting Blender API documentation."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent.load_knowledge_base()
        results = agent.get_blender_api_docs("mesh.primitive_cube_add")
        
        assert isinstance(results, list)
        for result in results:
            assert result.chunk.source == KnowledgeSource.BLENDER_API
    
    def test_get_mcp_examples(self, temp_dirs):
        """Test getting MCP examples."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        # Create empty knowledge base for this test
        agent.load_knowledge_base()
        results = agent.get_mcp_examples("protein")
        
        assert isinstance(results, list)
    
    def test_get_knowledge_stats(self, temp_dirs, sample_knowledge_content):
        """Test getting knowledge base statistics."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent.load_knowledge_base()
        stats = agent.get_knowledge_stats()
        
        assert "total_chunks" in stats
        assert "source_distribution" in stats
        assert "embedding_model" in stats
        assert "has_embeddings" in stats
        assert stats["total_chunks"] > 0
    
    def test_evaluate_retrieval_quality(self, temp_dirs, sample_knowledge_content):
        """Test retrieval quality evaluation."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        agent.load_knowledge_base()
        
        test_queries = [
            {"query": "create cube", "expected_topics": ["bpy.ops.mesh.primitive_cube_add"]},
            {"query": "sphere primitive", "expected_topics": ["bpy.ops.mesh.primitive_uv_sphere_add"]},
            {"query": "material creation", "expected_topics": ["bpy.data.materials.new"]}
        ]
        
        metrics = agent.evaluate_retrieval_quality(test_queries)
        
        assert "accuracy" in metrics
        assert "average_relevance_score" in metrics
        assert "total_queries" in metrics
        assert "correct_retrievals" in metrics
        assert 0 <= metrics["accuracy"] <= 1
        assert 0 <= metrics["average_relevance_score"] <= 1
    
    def test_evaluate_retrieval_quality_empty_queries(self, temp_dirs):
        """Test retrieval quality evaluation with empty queries."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        metrics = agent.evaluate_retrieval_quality([])
        
        assert "error" in metrics
    
    def test_knowledge_retrieval_error_handling(self, temp_dirs):
        """Test error handling in knowledge retrieval operations."""
        agent = KnowledgeAgent(
            knowledge_base_path=temp_dirs["knowledge_base"],
            db_path=temp_dirs["db"]
        )
        
        # Test with corrupted database path
        with patch.object(agent.collection, 'query', side_effect=Exception("Database error")):
            with pytest.raises(KnowledgeRetrievalError):
                agent.query_knowledge("test query")
    
    def test_force_reload_knowledge_base(self, temp_dirs, sample_knowledge_content, mock_openai_client):
        """Test force reloading knowledge base."""
        knowledge_file = Path(temp_dirs["knowledge_base"]) / "blender_docs_subset.txt"
        knowledge_file.write_text(sample_knowledge_content)
        
        with patch('agents.knowledge_agent.OpenAI', return_value=mock_openai_client):
            agent = KnowledgeAgent(
                knowledge_base_path=temp_dirs["knowledge_base"],
                db_path=temp_dirs["db"],
                openai_api_key="test_key"
            )
            
            # Load once
            agent.load_knowledge_base()
            initial_count = agent.collection.count()
            
            # Force reload
            result = agent.load_knowledge_base(force_reload=True)
            
            assert result is True
            assert agent.collection.count() >= initial_count
