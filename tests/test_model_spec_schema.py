"""
Unit tests for 3D model specification schema validation.

This module tests the JSON Schema definition and Pydantic models
for validating 3D model specifications according to task 1.3 requirements.
"""

import json
import pytest
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Import the models and validation utilities
from models.specs.v1.models import (
    ModelSpecification,
    Object3D,
    CubeGeometry,
    SphereGeometry,
    CylinderGeometry,
    PlaneGeometry,
    ConeGeometry,
    Material,
    Transform,
    Vector3,
    Color,
    ModelInfo,
    SceneSettings,
    UnitsEnum,
    GeometryTypeEnum,
    MaterialTypeEnum
)
from models.specs.version_manager import <PERSON><PERSON>aVersionManager, validate_spec


class TestBasicModels:
    """Test basic Pydantic model validation."""
    
    def test_vector3_validation(self):
        """Test Vector3 model validation."""
        # Valid vector
        vector = Vector3(x=1.0, y=2.0, z=3.0)
        assert vector.x == 1.0
        assert vector.y == 2.0
        assert vector.z == 3.0
        
        # Invalid vector (missing field)
        with pytest.raises(ValueError):
            Vector3(x=1.0, y=2.0)
    
    def test_color_validation(self):
        """Test Color model validation."""
        # Valid color
        color = Color(r=0.5, g=0.7, b=0.2, a=1.0)
        assert color.r == 0.5
        assert color.g == 0.7
        assert color.b == 0.2
        assert color.a == 1.0
        
        # Valid color with default alpha
        color_default_alpha = Color(r=0.5, g=0.7, b=0.2)
        assert color_default_alpha.a == 1.0
        
        # Invalid color (out of range)
        with pytest.raises(ValueError):
            Color(r=1.5, g=0.7, b=0.2)
        
        with pytest.raises(ValueError):
            Color(r=0.5, g=-0.1, b=0.2)
    
    def test_geometry_validation(self):
        """Test geometry model validation."""
        # Valid cube
        cube = CubeGeometry(size=2.0)
        assert cube.type == GeometryTypeEnum.CUBE
        assert cube.size == 2.0
        
        # Valid sphere
        sphere = SphereGeometry(radius=1.5, subdivisions=5)
        assert sphere.type == GeometryTypeEnum.SPHERE
        assert sphere.radius == 1.5
        assert sphere.subdivisions == 5
        
        # Valid cylinder
        cylinder = CylinderGeometry(radius=1.0, height=3.0, vertices=24)
        assert cylinder.type == GeometryTypeEnum.CYLINDER
        assert cylinder.radius == 1.0
        assert cylinder.height == 3.0
        assert cylinder.vertices == 24
        
        # Invalid geometry (negative size)
        with pytest.raises(ValueError):
            CubeGeometry(size=-1.0)
        
        # Invalid geometry (subdivisions out of range)
        with pytest.raises(ValueError):
            SphereGeometry(radius=1.0, subdivisions=15)
    
    def test_material_validation(self):
        """Test Material model validation."""
        # Valid basic material
        material = Material(
            type=MaterialTypeEnum.BASIC,
            name="Test Material",
            color=Color(r=0.8, g=0.2, b=0.2)
        )
        assert material.type == MaterialTypeEnum.BASIC
        assert material.name == "Test Material"
        assert material.metallic == 0.0  # default
        assert material.roughness == 0.5  # default
        
        # Valid PBR material
        pbr_material = Material(
            type=MaterialTypeEnum.PBR,
            metallic=0.8,
            roughness=0.2
        )
        assert pbr_material.type == MaterialTypeEnum.PBR
        assert pbr_material.metallic == 0.8
        assert pbr_material.roughness == 0.2
        
        # Invalid material (metallic out of range)
        with pytest.raises(ValueError):
            Material(type=MaterialTypeEnum.PBR, metallic=1.5)


class TestObject3D:
    """Test Object3D model validation."""
    
    def test_valid_object(self):
        """Test valid Object3D creation."""
        obj = Object3D(
            id="test_cube",
            name="Test Cube",
            geometry=CubeGeometry(size=2.0),
            transform=Transform(
                position=Vector3(x=0.0, y=0.0, z=0.0),
                rotation=Vector3(x=0.0, y=0.0, z=0.0),
                scale=Vector3(x=1.0, y=1.0, z=1.0)
            ),
            material=Material(
                type=MaterialTypeEnum.BASIC,
                color=Color(r=1.0, g=0.0, b=0.0)
            )
        )
        
        assert obj.id == "test_cube"
        assert obj.name == "Test Cube"
        assert obj.visible is True  # default
        assert obj.parent_id is None  # default
    
    def test_invalid_object_id(self):
        """Test invalid Object3D ID validation."""
        with pytest.raises(ValueError):
            Object3D(
                id="invalid id with spaces",
                name="Test",
                geometry=CubeGeometry()
            )
        
        with pytest.raises(ValueError):
            Object3D(
                id="invalid@id",
                name="Test",
                geometry=CubeGeometry()
            )


class TestModelSpecification:
    """Test complete ModelSpecification validation."""
    
    def test_valid_specification(self):
        """Test valid ModelSpecification creation."""
        spec = ModelSpecification(
            schema_version="v1.0.0",
            model_info=ModelInfo(
                name="Test Model",
                description="A test model"
            ),
            objects=[
                Object3D(
                    id="cube_01",
                    name="Test Cube",
                    geometry=CubeGeometry(size=2.0)
                )
            ]
        )
        
        assert spec.schema_version == "v1.0.0"
        assert spec.model_info.name == "Test Model"
        assert len(spec.objects) == 1
        assert spec.objects[0].id == "cube_01"
    
    def test_invalid_schema_version(self):
        """Test invalid schema version validation."""
        with pytest.raises(ValueError):
            ModelSpecification(
                schema_version="invalid_version",
                model_info=ModelInfo(name="Test", description="Test"),
                objects=[Object3D(id="test", name="Test", geometry=CubeGeometry())]
            )
    
    def test_empty_objects_list(self):
        """Test validation with empty objects list."""
        with pytest.raises(ValueError):
            ModelSpecification(
                schema_version="v1.0.0",
                model_info=ModelInfo(name="Test", description="Test"),
                objects=[]
            )
    
    def test_object_hierarchy_validation(self):
        """Test object parent-child relationship validation."""
        # Valid hierarchy
        spec = ModelSpecification(
            schema_version="v1.0.0",
            model_info=ModelInfo(name="Test", description="Test"),
            objects=[
                Object3D(id="parent", name="Parent", geometry=CubeGeometry()),
                Object3D(id="child", name="Child", geometry=SphereGeometry(), parent_id="parent")
            ]
        )
        assert len(spec.objects) == 2
        
        # Invalid hierarchy (non-existent parent)
        with pytest.raises(ValueError):
            ModelSpecification(
                schema_version="v1.0.0",
                model_info=ModelInfo(name="Test", description="Test"),
                objects=[
                    Object3D(id="child", name="Child", geometry=SphereGeometry(), parent_id="nonexistent")
                ]
            )
        
        # Invalid hierarchy (self-parent)
        with pytest.raises(ValueError):
            ModelSpecification(
                schema_version="v1.0.0",
                model_info=ModelInfo(name="Test", description="Test"),
                objects=[
                    Object3D(id="self_parent", name="Self Parent", geometry=CubeGeometry(), parent_id="self_parent")
                ]
            )


class TestExampleValidation:
    """Test validation of example specification files."""
    
    @pytest.fixture
    def examples_dir(self):
        """Get the examples directory path."""
        return Path(__file__).parent.parent / "models" / "specs" / "v1" / "examples"
    
    def load_example(self, examples_dir: Path, filename: str) -> Dict[str, Any]:
        """Load an example JSON file."""
        example_path = examples_dir / filename
        with open(example_path, 'r') as f:
            return json.load(f)
    
    def test_cube_example_validation(self, examples_dir):
        """Test cube example validation."""
        cube_data = self.load_example(examples_dir, "cube_example.json")
        
        # Test with Pydantic model
        spec = ModelSpecification(**cube_data)
        assert spec.schema_version == "v1.0.0"
        assert spec.model_info.name == "Simple Red Cube"
        assert len(spec.objects) == 1
        assert spec.objects[0].geometry.type == GeometryTypeEnum.CUBE
        
        # Test with version manager
        is_valid, errors = validate_spec(cube_data)
        assert is_valid, f"Validation errors: {errors}"
    
    def test_sphere_example_validation(self, examples_dir):
        """Test sphere example validation."""
        sphere_data = self.load_example(examples_dir, "sphere_example.json")
        
        spec = ModelSpecification(**sphere_data)
        assert spec.objects[0].geometry.type == GeometryTypeEnum.SPHERE
        assert spec.objects[0].material.type == MaterialTypeEnum.PBR
        
        is_valid, errors = validate_spec(sphere_data)
        assert is_valid, f"Validation errors: {errors}"
    
    def test_cylinder_example_validation(self, examples_dir):
        """Test cylinder example validation."""
        cylinder_data = self.load_example(examples_dir, "cylinder_example.json")
        
        spec = ModelSpecification(**cylinder_data)
        assert spec.objects[0].geometry.type == GeometryTypeEnum.CYLINDER
        
        is_valid, errors = validate_spec(cylinder_data)
        assert is_valid, f"Validation errors: {errors}"
    
    def test_cone_example_validation(self, examples_dir):
        """Test cone example validation."""
        cone_data = self.load_example(examples_dir, "cone_example.json")
        
        spec = ModelSpecification(**cone_data)
        assert spec.objects[0].geometry.type == GeometryTypeEnum.CONE
        assert spec.objects[0].material.emission is not None
        
        is_valid, errors = validate_spec(cone_data)
        assert is_valid, f"Validation errors: {errors}"
    
    def test_complex_scene_example_validation(self, examples_dir):
        """Test complex scene example validation."""
        complex_data = self.load_example(examples_dir, "complex_scene_example.json")
        
        spec = ModelSpecification(**complex_data)
        assert len(spec.objects) == 4
        
        # Check hierarchy
        child_sphere = next(obj for obj in spec.objects if obj.id == "child_sphere")
        assert child_sphere.parent_id == "central_cube"
        
        is_valid, errors = validate_spec(complex_data)
        assert is_valid, f"Validation errors: {errors}"


class TestVersionManager:
    """Test schema version management functionality."""
    
    def test_version_manager_initialization(self):
        """Test version manager initialization."""
        vm = SchemaVersionManager()
        assert vm.specs_root.exists()
    
    def test_get_available_versions(self):
        """Test getting available schema versions."""
        vm = SchemaVersionManager()
        versions = vm.get_available_versions()
        
        assert len(versions) >= 1
        assert any(v.version.startswith("v1.") for v in versions)
    
    def test_get_latest_version(self):
        """Test getting the latest schema version."""
        vm = SchemaVersionManager()
        latest = vm.get_latest_version()
        
        assert latest is not None
        assert latest.version.startswith("v1.")
    
    def test_version_format_validation(self):
        """Test version format validation."""
        vm = SchemaVersionManager()
        
        assert vm.validate_version_format("v1.0.0")
        assert vm.validate_version_format("1.0.0")
        assert vm.validate_version_format("v2.1.3")
        
        assert not vm.validate_version_format("invalid")
        assert not vm.validate_version_format("v1.0")
        assert not vm.validate_version_format("1.0.0.0")
    
    def test_version_compatibility(self):
        """Test version compatibility checking."""
        vm = SchemaVersionManager()
        
        # Same version should be compatible
        assert vm.is_compatible("v1.0.0", "v1.0.0")
        
        # Minor version increase should be compatible
        assert vm.is_compatible("v1.0.0", "v1.1.0")
        
        # Major version change should not be compatible
        assert not vm.is_compatible("v1.0.0", "v2.0.0")


if __name__ == "__main__":
    pytest.main([__file__])
