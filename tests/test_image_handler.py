"""
Comprehensive unit tests for ImageHandler module

Tests cover:
- Local image loading and processing
- Image standardization and normalization
- AI image generation API integration (mocked)
- Error handling and exception scenarios
- Input validation and edge cases
- Retry mechanisms and graceful degradation
"""

import unittest
import tempfile
import shutil
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from PIL import Image
import requests
import io

from input_module.image_handler import (
    ImageHandler, 
    ImageSource, 
    ImageFormat, 
    ImageMetadata,
    ImageProcessingError,
    APIError
)

class TestImageHandler(unittest.TestCase):
    """Test suite for ImageHandler class"""
    
    def setUp(self):
        """Set up test environment"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_output_dir = os.path.join(self.temp_dir, "test_output")
        self.handler = ImageHandler(
            output_dir=self.test_output_dir,
            openai_api_key="test_key"
        )
        
        # Create test images
        self.test_image_path = self._create_test_image("test_image.png", (512, 512))
        self.test_jpg_path = self._create_test_image("test_image.jpg", (300, 400))
        self.test_bmp_path = self._create_test_image("test_image.bmp", (100, 100))
    
    def tearDown(self):
        """Clean up test environment"""
        shutil.rmtree(self.temp_dir)
    
    def _create_test_image(self, filename: str, size: tuple) -> str:
        """Create a test image file"""
        image_path = os.path.join(self.temp_dir, filename)
        img = Image.new('RGB', size, color='red')
        img.save(image_path)
        return image_path
    
    def test_init_default_parameters(self):
        """Test ImageHandler initialization with default parameters"""
        handler = ImageHandler()
        self.assertEqual(handler.default_size, (256, 256))
        self.assertTrue(handler.output_dir.exists())
    
    def test_init_custom_parameters(self):
        """Test ImageHandler initialization with custom parameters"""
        custom_size = (512, 512)
        handler = ImageHandler(
            output_dir=self.test_output_dir,
            default_size=custom_size
        )
        self.assertEqual(handler.default_size, custom_size)
        self.assertEqual(str(handler.output_dir), self.test_output_dir)
    
    def test_validate_image_format_supported(self):
        """Test validation of supported image formats"""
        self.assertTrue(self.handler.validate_image_format("test.png"))
        self.assertTrue(self.handler.validate_image_format("test.jpg"))
        self.assertTrue(self.handler.validate_image_format("test.jpeg"))
        self.assertTrue(self.handler.validate_image_format("test.bmp"))
        self.assertTrue(self.handler.validate_image_format("TEST.PNG"))  # Case insensitive
    
    def test_validate_image_format_unsupported(self):
        """Test validation of unsupported image formats"""
        self.assertFalse(self.handler.validate_image_format("test.gif"))
        self.assertFalse(self.handler.validate_image_format("test.tiff"))
        self.assertFalse(self.handler.validate_image_format("test.webp"))
        self.assertFalse(self.handler.validate_image_format("test.txt"))
    
    def test_get_image_info_success(self):
        """Test getting image information successfully"""
        info = self.handler.get_image_info(self.test_image_path)
        
        self.assertIn('format', info)
        self.assertIn('mode', info)
        self.assertIn('size', info)
        self.assertIn('filename', info)
        self.assertIn('file_size', info)
        self.assertEqual(info['size'], (512, 512))
    
    def test_get_image_info_file_not_found(self):
        """Test getting image info for non-existent file"""
        with self.assertRaises(ImageProcessingError):
            self.handler.get_image_info("non_existent.png")
    
    def test_process_local_image_png_success(self):
        """Test successful processing of local PNG image"""
        metadata = self.handler.process_image(
            self.test_image_path,
            source_type=ImageSource.LOCAL,
            target_size=(256, 256)
        )
        
        self.assertEqual(metadata.source, ImageSource.LOCAL)
        self.assertEqual(metadata.original_path, self.test_image_path)
        self.assertEqual(metadata.size, (256, 256))
        self.assertTrue(os.path.exists(metadata.processed_path))
    
    def test_process_local_image_jpg_success(self):
        """Test successful processing of local JPG image"""
        metadata = self.handler.process_image(
            self.test_jpg_path,
            source_type=ImageSource.LOCAL,
            target_size=(128, 128)
        )
        
        self.assertEqual(metadata.source, ImageSource.LOCAL)
        self.assertEqual(metadata.size, (128, 128))
        self.assertTrue(os.path.exists(metadata.processed_path))
    
    def test_process_local_image_bmp_success(self):
        """Test successful processing of local BMP image"""
        metadata = self.handler.process_image(
            self.test_bmp_path,
            source_type=ImageSource.LOCAL
        )
        
        self.assertEqual(metadata.source, ImageSource.LOCAL)
        self.assertEqual(metadata.size, (256, 256))  # Default size
        self.assertTrue(os.path.exists(metadata.processed_path))
    
    def test_process_local_image_file_not_found(self):
        """Test processing non-existent local image"""
        with self.assertRaises(ImageProcessingError):
            self.handler.process_image(
                "non_existent.png",
                source_type=ImageSource.LOCAL
            )
    
    def test_process_local_image_unsupported_format(self):
        """Test processing local image with unsupported format"""
        # Create a text file with image extension
        fake_image_path = os.path.join(self.temp_dir, "fake.gif")
        with open(fake_image_path, 'w') as f:
            f.write("not an image")
        
        with self.assertRaises(ImageProcessingError):
            self.handler.process_image(
                fake_image_path,
                source_type=ImageSource.LOCAL
            )
    
    def test_process_local_image_corrupted_file(self):
        """Test processing corrupted local image file"""
        # Create a corrupted image file
        corrupted_path = os.path.join(self.temp_dir, "corrupted.png")
        with open(corrupted_path, 'wb') as f:
            f.write(b"corrupted image data")
        
        with self.assertRaises(ImageProcessingError):
            self.handler.process_image(
                corrupted_path,
                source_type=ImageSource.LOCAL
            )
    
    @patch('requests.get')
    def test_process_url_image_success(self, mock_get):
        """Test successful processing of URL image"""
        # Mock successful HTTP response
        mock_response = Mock()
        mock_response.content = self._get_image_bytes()
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        metadata = self.handler.process_image(
            "https://example.com/image.png",
            source_type=ImageSource.URL,
            target_size=(200, 200)
        )
        
        self.assertEqual(metadata.source, ImageSource.URL)
        self.assertEqual(metadata.original_path, "https://example.com/image.png")
        self.assertEqual(metadata.size, (200, 200))
        self.assertTrue(os.path.exists(metadata.processed_path))
    
    @patch('requests.get')
    def test_process_url_image_download_failure(self, mock_get):
        """Test URL image processing with download failure"""
        mock_get.side_effect = requests.RequestException("Network error")
        
        with self.assertRaises(ImageProcessingError):
            self.handler.process_image(
                "https://example.com/image.png",
                source_type=ImageSource.URL
            )
    
    @patch('requests.get')
    def test_process_url_image_retry_mechanism(self, mock_get):
        """Test URL image processing retry mechanism"""
        # First two calls fail, third succeeds
        mock_response = Mock()
        mock_response.content = self._get_image_bytes()
        mock_response.raise_for_status.return_value = None
        
        mock_get.side_effect = [
            requests.RequestException("First failure"),
            requests.RequestException("Second failure"),
            mock_response
        ]
        
        with patch('time.sleep'):  # Speed up test by mocking sleep
            metadata = self.handler.process_image(
                "https://example.com/image.png",
                source_type=ImageSource.URL
            )
        
        self.assertEqual(metadata.source, ImageSource.URL)
        self.assertEqual(mock_get.call_count, 3)
    
    @patch('input_module.image_handler.OpenAI')
    @patch('requests.get')
    def test_process_ai_generated_image_success(self, mock_get, mock_openai_class):
        """Test successful AI image generation and processing"""
        # Mock OpenAI client and response
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        mock_dalle_response = Mock()
        mock_dalle_response.data = [Mock(url="https://example.com/generated.png")]
        mock_client.images.generate.return_value = mock_dalle_response
        
        # Mock image download
        mock_http_response = Mock()
        mock_http_response.content = self._get_image_bytes()
        mock_http_response.raise_for_status.return_value = None
        mock_get.return_value = mock_http_response
        
        # Create handler with mocked OpenAI client
        handler = ImageHandler(output_dir=self.test_output_dir, openai_api_key="test_key")
        handler.openai_client = mock_client
        
        metadata = handler.process_image(
            "A red cube on a white background",
            source_type=ImageSource.AI_GENERATED,
            ai_params={'quality': 'hd'}
        )
        
        self.assertEqual(metadata.source, ImageSource.AI_GENERATED)
        self.assertIsNone(metadata.original_path)
        self.assertIsNotNone(metadata.ai_generation_params)
        self.assertEqual(metadata.ai_generation_params['prompt'], "A red cube on a white background")
        self.assertTrue(os.path.exists(metadata.processed_path))
    
    def test_process_ai_generated_image_no_client(self):
        """Test AI image generation without OpenAI client"""
        handler = ImageHandler(output_dir=self.test_output_dir)  # No API key
        
        with self.assertRaises(APIError):
            handler.process_image(
                "A red cube",
                source_type=ImageSource.AI_GENERATED
            )
    
    @patch('input_module.image_handler.OpenAI')
    def test_process_ai_generated_image_api_failure(self, mock_openai_class):
        """Test AI image generation with API failure"""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        mock_client.images.generate.side_effect = Exception("API Error")
        
        handler = ImageHandler(output_dir=self.test_output_dir, openai_api_key="test_key")
        handler.openai_client = mock_client
        
        with self.assertRaises(APIError):
            handler.process_image(
                "A red cube",
                source_type=ImageSource.AI_GENERATED
            )
    
    @patch('input_module.image_handler.OpenAI')
    @patch('requests.get')
    def test_ai_image_retry_mechanism(self, mock_get, mock_openai_class):
        """Test AI image generation retry mechanism"""
        mock_client = Mock()
        mock_openai_class.return_value = mock_client
        
        # Mock successful response after retries
        mock_dalle_response = Mock()
        mock_dalle_response.data = [Mock(url="https://example.com/generated.png")]
        
        mock_client.images.generate.side_effect = [
            Exception("First failure"),
            Exception("Second failure"),
            mock_dalle_response
        ]
        
        # Mock image download
        mock_http_response = Mock()
        mock_http_response.content = self._get_image_bytes()
        mock_http_response.raise_for_status.return_value = None
        mock_get.return_value = mock_http_response
        
        handler = ImageHandler(output_dir=self.test_output_dir, openai_api_key="test_key")
        handler.openai_client = mock_client
        
        with patch('time.sleep'):  # Speed up test
            metadata = handler.process_image(
                "A red cube",
                source_type=ImageSource.AI_GENERATED
            )
        
        self.assertEqual(metadata.source, ImageSource.AI_GENERATED)
        self.assertEqual(mock_client.images.generate.call_count, 3)
    
    def test_unsupported_source_type(self):
        """Test processing with unsupported source type"""
        with self.assertRaises(ImageProcessingError):
            self.handler.process_image(
                "test_input",
                source_type="unsupported_source"
            )
    
    def _get_image_bytes(self) -> bytes:
        """Helper method to get image bytes for mocking"""
        img = Image.new('RGB', (100, 100), color='blue')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        return img_bytes.getvalue()

if __name__ == '__main__':
    unittest.main()
