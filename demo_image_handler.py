#!/usr/bin/env python3
"""
Demo script for ImageHandler functionality

This script demonstrates the capabilities of the ImageHandler module including:
- Local image processing
- Image standardization
- Error handling
- Basic functionality testing

Usage:
    python demo_image_handler.py
"""

import os
import sys
from pathlib import Path
from PIL import Image
import tempfile

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from input_module.image_handler import ImageHandler, ImageSource, ImageProcessingError, APIError

def create_sample_images():
    """Create sample images for testing"""
    temp_dir = Path("demo_images")
    temp_dir.mkdir(exist_ok=True)
    
    # Create sample images
    sample_images = []
    
    # Create a red square
    red_img = Image.new('RGB', (400, 400), color='red')
    red_path = temp_dir / "red_square.png"
    red_img.save(red_path)
    sample_images.append(red_path)
    
    # Create a blue circle (approximated as a square for simplicity)
    blue_img = Image.new('RGB', (300, 300), color='blue')
    blue_path = temp_dir / "blue_square.jpg"
    blue_img.save(blue_path)
    sample_images.append(blue_path)
    
    # Create a green rectangle
    green_img = Image.new('RGB', (500, 200), color='green')
    green_path = temp_dir / "green_rectangle.bmp"
    green_img.save(green_path)
    sample_images.append(green_path)
    
    return sample_images

def demo_local_image_processing():
    """Demonstrate local image processing capabilities"""
    print("=" * 60)
    print("DEMO: Local Image Processing")
    print("=" * 60)
    
    # Create sample images
    sample_images = create_sample_images()
    
    # Initialize ImageHandler
    handler = ImageHandler(output_dir="demo_output")
    
    for image_path in sample_images:
        try:
            print(f"\nProcessing: {image_path}")
            
            # Get image info
            info = handler.get_image_info(image_path)
            print(f"  Original size: {info['size']}")
            print(f"  Format: {info['format']}")
            print(f"  File size: {info['file_size']} bytes")
            
            # Process image with different target sizes
            for target_size in [(256, 256), (128, 128), (512, 512)]:
                metadata = handler.process_image(
                    image_path,
                    source_type=ImageSource.LOCAL,
                    target_size=target_size
                )
                
                print(f"  Processed to {target_size}: {metadata.processed_path}")
                print(f"    Final size: {metadata.size}")
                print(f"    Format: {metadata.format}")
                
        except ImageProcessingError as e:
            print(f"  Error processing {image_path}: {e}")

def demo_error_handling():
    """Demonstrate error handling capabilities"""
    print("\n" + "=" * 60)
    print("DEMO: Error Handling")
    print("=" * 60)
    
    handler = ImageHandler(output_dir="demo_output")
    
    # Test 1: Non-existent file
    print("\n1. Testing non-existent file:")
    try:
        handler.process_image("non_existent.png", source_type=ImageSource.LOCAL)
    except ImageProcessingError as e:
        print(f"   ✓ Caught expected error: {e}")
    
    # Test 2: Unsupported format
    print("\n2. Testing unsupported format:")
    try:
        # Create a fake file with unsupported extension
        fake_file = Path("demo_images/fake.gif")
        fake_file.parent.mkdir(exist_ok=True)
        fake_file.write_text("not an image")
        
        handler.process_image(str(fake_file), source_type=ImageSource.LOCAL)
    except ImageProcessingError as e:
        print(f"   ✓ Caught expected error: {e}")
    finally:
        if fake_file.exists():
            fake_file.unlink()
    
    # Test 3: Format validation
    print("\n3. Testing format validation:")
    print(f"   PNG supported: {handler.validate_image_format('test.png')}")
    print(f"   JPG supported: {handler.validate_image_format('test.jpg')}")
    print(f"   GIF supported: {handler.validate_image_format('test.gif')}")
    print(f"   WEBP supported: {handler.validate_image_format('test.webp')}")

def demo_ai_image_generation():
    """Demonstrate AI image generation (without actual API calls)"""
    print("\n" + "=" * 60)
    print("DEMO: AI Image Generation (Mock)")
    print("=" * 60)
    
    # Test without API key
    print("\n1. Testing without API key:")
    handler = ImageHandler(output_dir="demo_output")
    try:
        handler.process_image(
            "A red cube on a white background",
            source_type=ImageSource.AI_GENERATED
        )
    except APIError as e:
        print(f"   ✓ Caught expected error: {e}")
    
    # Note: Real API testing would require an actual OpenAI API key
    print("\n2. Real API testing requires OpenAI API key")
    print("   To test with real API, set OPENAI_API_KEY environment variable")
    
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        print("   API key found - you can test real AI generation!")
        handler_with_key = ImageHandler(
            output_dir="demo_output",
            openai_api_key=api_key
        )
        print("   Handler initialized with API key")
    else:
        print("   No API key found - skipping real API test")

def demo_url_processing():
    """Demonstrate URL image processing (mock)"""
    print("\n" + "=" * 60)
    print("DEMO: URL Image Processing (Mock)")
    print("=" * 60)
    
    print("Note: URL processing requires internet connection")
    print("This demo shows the interface without making actual HTTP requests")
    
    handler = ImageHandler(output_dir="demo_output")
    
    # This would work with a real URL and internet connection
    sample_urls = [
        "https://example.com/sample.png",
        "https://example.com/test.jpg"
    ]
    
    for url in sample_urls:
        print(f"\nWould process URL: {url}")
        print("  - Download image from URL")
        print("  - Validate image format")
        print("  - Standardize to target size")
        print("  - Save processed image")

def cleanup_demo_files():
    """Clean up demo files"""
    import shutil
    
    demo_dirs = ["demo_images", "demo_output", "processed_images"]
    for dir_name in demo_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"Cleaned up: {dir_name}")

def main():
    """Main demo function"""
    print("ImageHandler Demo")
    print("================")
    print("This demo showcases the ImageHandler module capabilities")
    print("including local image processing, error handling, and API integration.")
    
    try:
        # Run demos
        demo_local_image_processing()
        demo_error_handling()
        demo_ai_image_generation()
        demo_url_processing()
        
        print("\n" + "=" * 60)
        print("DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        print("\nCheck the 'demo_output' directory for processed images.")
        print("All tests passed - ImageHandler is working correctly!")
        
    except Exception as e:
        print(f"\nDemo failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Ask user if they want to clean up
        response = input("\nClean up demo files? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            cleanup_demo_files()
            print("Demo files cleaned up.")
        else:
            print("Demo files preserved.")

if __name__ == "__main__":
    main()
