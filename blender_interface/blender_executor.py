"""
Blender Executor Module

This module provides the BlenderExecutor class for executing Blender scripts
in headless mode and parsing the output for structured information.

Author: AI Agent System
Date: 2025-07-17
"""

import os
import subprocess
import tempfile
import json
import re
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from pathlib import Path


class BlenderExecutionStatus(Enum):
    """Enumeration for Blender execution status."""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    SCRIPT_ERROR = "script_error"
    BLENDER_NOT_FOUND = "blender_not_found"


@dataclass
class BlenderOutput:
    """Data class for structured Blender execution output."""
    status: BlenderExecutionStatus
    stdout: str
    stderr: str
    return_code: int
    execution_time: float
    output_files: List[str]
    error_details: Optional[Dict[str, Any]] = None
    parsed_info: Optional[Dict[str, Any]] = None


class BlenderExecutionError(Exception):
    """Custom exception for Blender execution errors."""
    
    def __init__(self, message: str, output: Optional[BlenderOutput] = None):
        super().__init__(message)
        self.output = output


class BlenderExecutor:
    """
    Blender Executor class for running Blender scripts and parsing output.
    
    This class provides functionality to:
    - Execute Blender scripts in headless mode
    - Capture and parse stdout/stderr output
    - Extract structured information from Blender output
    - Handle various error scenarios
    """
    
    def __init__(self, blender_path: Optional[str] = None, timeout: int = 300):
        """
        Initialize the BlenderExecutor.
        
        Args:
            blender_path: Path to Blender executable. If None, will try to get from environment.
            timeout: Maximum execution time in seconds (default: 300)
        """
        self.blender_path = self._get_blender_path(blender_path)
        self.timeout = timeout
        self.logger = logging.getLogger(__name__)
        
        # Validate Blender installation
        self._validate_blender_installation()
    
    def _get_blender_path(self, blender_path: Optional[str]) -> str:
        """Get Blender executable path from parameter or environment."""
        if blender_path:
            return blender_path
        
        # Try environment variable
        env_path = os.getenv("BLENDER_PATH")
        if env_path:
            return env_path
        
        # Try common installation paths
        common_paths = [
            "/usr/bin/blender",
            "/usr/local/bin/blender",
            "/opt/blender/blender",
            "/Applications/Blender.app/Contents/MacOS/Blender",
            "C:\\Program Files\\Blender Foundation\\Blender\\blender.exe"
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        raise BlenderExecutionError("Blender executable not found. Please set BLENDER_PATH environment variable.")
    
    def _validate_blender_installation(self) -> None:
        """Validate that Blender is properly installed and accessible."""
        if not os.path.exists(self.blender_path):
            raise BlenderExecutionError(f"Blender executable not found at: {self.blender_path}")
        
        if not os.access(self.blender_path, os.X_OK):
            raise BlenderExecutionError(f"Blender executable is not executable: {self.blender_path}")
    
    def execute_script(self, script_content: str, output_dir: Optional[str] = None) -> BlenderOutput:
        """
        Execute a Blender Python script and return structured output.
        
        Args:
            script_content: Python script content to execute in Blender
            output_dir: Directory for output files (optional)
        
        Returns:
            BlenderOutput object with execution results
        """
        import time
        start_time = time.time()
        
        # Create temporary script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_script:
            temp_script.write(script_content)
            temp_script_path = temp_script.name
        
        try:
            # Prepare command
            command = [
                self.blender_path,
                "--background",  # Run in background (headless)
                "--python", temp_script_path
            ]
            
            self.logger.info(f"Executing Blender command: {' '.join(command)}")
            
            # Execute command
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=self.timeout,
                cwd=output_dir
            )
            
            execution_time = time.time() - start_time
            
            # Parse output and determine status
            output = self._parse_blender_output(
                result.stdout,
                result.stderr,
                result.returncode,
                execution_time,
                output_dir
            )
            
            self.logger.info(f"Blender execution completed with status: {output.status}")
            return output
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            self.logger.error(f"Blender execution timed out after {self.timeout} seconds")
            return BlenderOutput(
                status=BlenderExecutionStatus.TIMEOUT,
                stdout="",
                stderr=f"Execution timed out after {self.timeout} seconds",
                return_code=-1,
                execution_time=execution_time,
                output_files=[],
                error_details={"timeout": self.timeout}
            )
        
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Unexpected error during Blender execution: {e}")
            return BlenderOutput(
                status=BlenderExecutionStatus.ERROR,
                stdout="",
                stderr=str(e),
                return_code=-1,
                execution_time=execution_time,
                output_files=[],
                error_details={"exception": str(e)}
            )
        
        finally:
            # Clean up temporary script file
            try:
                os.unlink(temp_script_path)
            except OSError:
                pass
    
    def _parse_blender_output(self, stdout: str, stderr: str, return_code: int, 
                            execution_time: float, output_dir: Optional[str]) -> BlenderOutput:
        """
        Parse Blender output and extract structured information.
        
        Args:
            stdout: Standard output from Blender
            stderr: Standard error from Blender
            return_code: Process return code
            execution_time: Execution time in seconds
            output_dir: Output directory path
        
        Returns:
            BlenderOutput object with parsed information
        """
        # Determine execution status
        if return_code == 0:
            # Even if return code is 0, check for Python errors in stderr
            if "Error:" in stderr or "Traceback" in stderr or "Exception" in stderr:
                status = BlenderExecutionStatus.SCRIPT_ERROR
            else:
                status = BlenderExecutionStatus.SUCCESS
        else:
            status = BlenderExecutionStatus.ERROR
            if "Error:" in stderr or "Traceback" in stderr:
                status = BlenderExecutionStatus.SCRIPT_ERROR
        
        # Find output files
        output_files = self._find_output_files(stdout, output_dir)
        
        # Parse error details
        error_details = None
        if status != BlenderExecutionStatus.SUCCESS:
            error_details = self._parse_error_details(stderr, stdout)
        
        # Parse additional information
        parsed_info = self._parse_additional_info(stdout)
        
        return BlenderOutput(
            status=status,
            stdout=stdout,
            stderr=stderr,
            return_code=return_code,
            execution_time=execution_time,
            output_files=output_files,
            error_details=error_details,
            parsed_info=parsed_info
        )
    
    def _find_output_files(self, stdout: str, output_dir: Optional[str]) -> List[str]:
        """Find output files mentioned in stdout or created in output directory."""
        output_files = []

        # Look for file paths in stdout
        file_patterns = [
            r"Saved '([^']+)'",
            r"Saved: ([^\s]+)",
            r"Written: ([^\s]+)",
            r"Exported: ([^\s]+)",
            r"Successfully saved.*to ([^\s]+)",
            r"Successfully saved Blender file to ([^\s]+)",
            r"saved to ([^\s\n]+)"
        ]

        for pattern in file_patterns:
            matches = re.findall(pattern, stdout, re.IGNORECASE)
            output_files.extend(matches)

        # Look for common file extensions in output directory
        if output_dir and os.path.exists(output_dir):
            common_extensions = ['.blend', '.obj', '.fbx', '.dae', '.ply', '.stl', '.png', '.jpg', '.exr']
            for file in os.listdir(output_dir):
                if any(file.lower().endswith(ext) for ext in common_extensions):
                    output_files.append(os.path.join(output_dir, file))

        return list(set(output_files))  # Remove duplicates
    
    def _parse_error_details(self, stderr: str, stdout: str) -> Dict[str, Any]:
        """Parse error details from stderr and stdout."""
        error_details = {}
        
        # Parse Python traceback
        if "Traceback" in stderr:
            traceback_match = re.search(r"Traceback \(most recent call last\):(.*?)(?=\n\S|\Z)", 
                                      stderr, re.DOTALL)
            if traceback_match:
                error_details["traceback"] = traceback_match.group(1).strip()
        
        # Parse specific error types
        error_patterns = {
            "syntax_error": r"SyntaxError: (.+)",
            "name_error": r"NameError: (.+)",
            "attribute_error": r"AttributeError: (.+)",
            "type_error": r"TypeError: (.+)",
            "value_error": r"ValueError: (.+)",
            "runtime_error": r"RuntimeError: (.+)"
        }
        
        for error_type, pattern in error_patterns.items():
            match = re.search(pattern, stderr)
            if match:
                error_details[error_type] = match.group(1)
        
        # Parse Blender-specific errors
        blender_error_patterns = {
            "addon_error": r"addon utils modules: (.+)",
            "bpy_error": r"bpy\.(.+?): (.+)",
            "mesh_error": r"mesh\.(.+?): (.+)"
        }
        
        for error_type, pattern in blender_error_patterns.items():
            match = re.search(pattern, stderr)
            if match:
                error_details[error_type] = match.group(0)
        
        return error_details
    
    def _parse_additional_info(self, stdout: str) -> Dict[str, Any]:
        """Parse additional information from stdout."""
        info = {}
        
        # Parse object creation info
        object_patterns = {
            "objects_created": r"Created (\d+) objects?",
            "meshes_created": r"Created (\d+) meshes?",
            "materials_created": r"Created (\d+) materials?"
        }
        
        for info_type, pattern in object_patterns.items():
            match = re.search(pattern, stdout, re.IGNORECASE)
            if match:
                info[info_type] = int(match.group(1))
        
        # Parse render info
        render_patterns = {
            "render_time": r"Time: (\d+:\d+\.\d+)",
            "samples": r"Sample (\d+)/(\d+)",
            "memory_usage": r"Mem:(\d+\.\d+)M"
        }
        
        for info_type, pattern in render_patterns.items():
            match = re.search(pattern, stdout)
            if match:
                info[info_type] = match.groups()
        
        return info
    
    def execute_script_file(self, script_path: str, output_dir: Optional[str] = None) -> BlenderOutput:
        """
        Execute a Blender Python script from file.
        
        Args:
            script_path: Path to Python script file
            output_dir: Directory for output files (optional)
        
        Returns:
            BlenderOutput object with execution results
        """
        if not os.path.exists(script_path):
            raise BlenderExecutionError(f"Script file not found: {script_path}")
        
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
        
        return self.execute_script(script_content, output_dir)
    
    def get_blender_version(self) -> str:
        """Get Blender version information."""
        try:
            result = subprocess.run(
                [self.blender_path, "--version"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # Extract version from output
                version_match = re.search(r"Blender (\d+\.\d+\.\d+)", result.stdout)
                if version_match:
                    return version_match.group(1)
            
            return "Unknown"
        
        except Exception:
            return "Unknown"
